'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('lessons', 'order', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      after: 'illustration_url'
    });

    // Update existing lessons to have proper order values
    // This will set order based on creation date for existing lessons
    await queryInterface.sequelize.query(`
      UPDATE lessons 
      SET "order" = subquery.row_number 
      FROM (
        SELECT id, ROW_NUMBER() OVER (PARTITION BY course_id ORDER BY created_at) - 1 as row_number
        FROM lessons
      ) AS subquery 
      WHERE lessons.id = subquery.id;
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('lessons', 'order');
  }
};
