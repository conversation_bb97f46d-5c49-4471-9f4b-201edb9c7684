import { Router } from 'express';
import { authenticate } from '../middleware/authenticate';
import { QuizController } from '../controllers/quizController';
import {
  validateCreateQuiz,
  validateUpdateQuiz,
  validateQuizId,
  validateLessonId,
  validateBulkDelete,
  validateQuizSubmission,
  validateQuizQuery
} from '../middleware/quizValidation';

const router = Router();
const quizController = new QuizController();

// Public routes
router.get('/', validateQuizQuery, quizController.getAllQuizzes);
router.get('/lesson/:lessonId/quiz', validateLessonId, quizController.getQuizByLesson);
router.get('/:id', validateQuizId, quizController.getQuizById);

// Protected routes
router.use(authenticate);
router.post('/', validateCreateQuiz, quizController.createQuiz);
router.put('/:id', validateUpdateQuiz, quizController.updateQuiz);
router.delete('/:id', validateQuizId, quizController.deleteQuiz);
router.post('/:id/submit', validateQuizSubmission, quizController.submitQuiz);

// Bulk operations
router.post('/bulk/delete', validateBulkDelete, quizController.bulkDeleteQuizzes);
router.post('/:id/duplicate', validateQuizId, quizController.duplicateQuiz);

export { router as quizRoutes };
