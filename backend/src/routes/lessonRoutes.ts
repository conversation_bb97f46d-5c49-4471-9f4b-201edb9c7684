import { Router } from 'express';
import { authenticate } from '../middleware/authenticate';
import { LessonController } from '../controllers/lessonController';
import {
  validateCreateLesson,
  validateUpdateLesson,
  validateLessonId,
  validateCourseId,
  validateLessonOrder,
  validateBulkDelete,
  validateLessonQuery
} from '../middleware/lessonValidation';

const router = Router();
const lessonController = new LessonController();

// Public routes
router.get('/', validateLessonQuery, lessonController.getAllLessons);
router.get('/course/:courseId/lessons', validateCourseId, lessonController.getLessonsByCourse);
router.get('/:id', validateLessonId, lessonController.getLessonById);

// Protected routes
router.use(authenticate);
router.post('/', validateCreateLesson, lessonController.createLesson);
router.put('/:id', validateUpdateLesson, lessonController.updateLesson);
router.delete('/:id', validateLessonId, lessonController.deleteLesson);

// Bulk operations
router.post('/bulk/delete', validateBulkDelete, lessonController.bulkDeleteLessons);
router.post('/:id/duplicate', validateLessonId, lessonController.duplicateLesson);

// Lesson ordering
router.patch('/course/:courseId/order', validateLessonOrder, lessonController.updateLessonOrder);

export { router as lessonRoutes };
