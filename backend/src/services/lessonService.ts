import { BaseService } from './baseService';
import { CustomError } from '../utils/customError';
import { PaginationQuery } from '../types/api';
import Lesson from '../models/lesson';
import Course from '../models/course';
import Quiz from '../models/quiz';
import UserProgress from '../models/userProgress';
import { Op } from 'sequelize';

interface CreateLessonInput {
  courseId: string;
  title: string;
  content: string;
  thumbnailUrl?: string;
  illustrationUrl?: string;
  order?: number;
}

interface LessonFilters extends PaginationQuery {
  search?: string;
  courseId?: string;
}

interface UpdateLessonOrderInput {
  id: string;
  order: number;
}

export class LessonService extends BaseService {
  async getAllLessons(query: LessonFilters): Promise<{
    lessons: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const { offset, limit, page } = this.getPaginationParams(query);
      const { search, courseId } = query;

      // Build where clause for filtering
      const whereClause: any = {};

      if (search) {
        whereClause[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { content: { [Op.iLike]: `%${search}%` } }
        ];
      }

      if (courseId) {
        whereClause.courseId = courseId;
      }

      const { count, rows } = await Lesson.findAndCountAll({
        where: whereClause,
        offset,
        limit,
        order: [['order', 'ASC'], ['created_at', 'DESC']],
        include: [{
          model: Course,
          as: 'course',
          attributes: ['id', 'title', 'level', 'status']
        }]
      });

      const totalPages = Math.ceil(count / limit);

      return {
        lessons: rows,
        total: count,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getLessonsByCourse(courseId: string): Promise<Lesson[]> {
    try {
      return await Lesson.findAll({
        where: { courseId },
        order: [['order', 'ASC'], ['created_at', 'ASC']]
      });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getLessonById(id: string): Promise<Lesson> {
    try {
      const lesson = await Lesson.findByPk(id, {
        include: [
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'title']
          },
          {
            model: Quiz,
            as: 'quizzes',
            attributes: ['id', 'title']
          }
        ]
      });

      if (!lesson) {
        throw new CustomError('Lesson not found', 404);
      }

      return lesson;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async createLesson(data: CreateLessonInput): Promise<Lesson> {
    try {
      // Verify course exists
      const courseExists = await Course.findByPk(data.courseId);

      if (!courseExists) {
        throw new CustomError('Course not found', 404);
      }

      // If no order is specified, set it to the next available order for this course
      if (data.order === undefined) {
        const maxOrder = await Lesson.max('order', {
          where: { courseId: data.courseId }
        }) as number;
        data.order = (maxOrder || 0) + 1;
      }

      return await Lesson.create(data);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateLesson(id: string, data: Partial<CreateLessonInput>): Promise<Lesson> {
    try {
      if (data.courseId) {
        // Verify course exists if courseId is being updated
        const courseExists = await Course.findByPk(data.courseId);

        if (!courseExists) {
          throw new CustomError('Course not found', 404);
        }
      }

      const lesson = await Lesson.findByPk(id);
      if (!lesson) {
        throw new CustomError('Lesson not found', 404);
      }

      return await lesson.update(data);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteLesson(id: string): Promise<void> {
    try {
      const lesson = await Lesson.findByPk(id);
      if (!lesson) {
        throw new CustomError('Lesson not found', 404);
      }

      await lesson.destroy();
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateLessonOrder(courseId: string, updates: UpdateLessonOrderInput[]): Promise<void> {
    try {
      // Verify course exists
      const courseExists = await Course.findByPk(courseId);
      if (!courseExists) {
        throw new CustomError('Course not found', 404);
      }

      // Update each lesson's order
      for (const update of updates) {
        await Lesson.update(
          { order: update.order },
          { where: { id: update.id, courseId } }
        );
      }
    } catch (error) {
      this.handleError(error);
    }
  }

  async bulkDeleteLessons(lessonIds: string[]): Promise<void> {
    try {
      if (!lessonIds || lessonIds.length === 0) {
        throw new CustomError('No lesson IDs provided', 400);
      }

      await Lesson.destroy({
        where: {
          id: {
            [Op.in]: lessonIds
          }
        }
      });
    } catch (error) {
      this.handleError(error);
    }
  }

  async duplicateLesson(lessonId: string): Promise<Lesson> {
    try {
      const originalLesson = await Lesson.findByPk(lessonId);
      if (!originalLesson) {
        throw new CustomError('Lesson not found', 404);
      }

      // Get the next order number for this course
      const maxOrder = await Lesson.max('order', {
        where: { courseId: originalLesson.courseId }
      }) as number;

      // Create a duplicate with modified title and new order
      const duplicateData = {
        courseId: originalLesson.courseId,
        title: `${originalLesson.title} (Copy)`,
        content: originalLesson.content,
        thumbnailUrl: originalLesson.thumbnailUrl,
        illustrationUrl: originalLesson.illustrationUrl,
        order: (maxOrder || 0) + 1
      };

      return await Lesson.create(duplicateData);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async markLessonComplete(lessonId: string, userId: string): Promise<void> {
    try {
      const progress = await UserProgress.findOne({
        where: {
          userId,
          lessonId
        }
      });

      if (progress) {
        // Update existing progress
        await progress.update({
          completed: true
        });
      } else {
        // Create new progress
        await UserProgress.create({
          userId,
          lessonId,
          completed: true,
          score: 0,
          streak: 0
        });
      }
    } catch (error) {
      this.handleError(error);
    }
  }
}
