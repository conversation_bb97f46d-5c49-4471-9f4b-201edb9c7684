import { Request, Response, NextFunction } from 'express';
import { LessonService } from '../services/lessonService';
import { CustomError } from '../utils/customError';

export class LessonController {
  private lessonService: LessonService;

  constructor() {
    this.lessonService = new LessonService();
  }

  getAllLessons = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const lessons = await this.lessonService.getAllLessons(req.query);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lessons
      });
    } catch (error) {
      next(error);
    }
  };

  getLessonsByCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { courseId } = req.params;
      const lessons = await this.lessonService.getLessonsByCourse(courseId);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lessons
      });
    } catch (error) {
      next(error);
    }
  };

  getLessonById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const lesson = await this.lessonService.getLessonById(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };

  createLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { courseId, title, content, thumbnailUrl, illustrationUrl, order } = req.body;

      if (!courseId || !title || !content) {
        throw new CustomError('Missing required fields', 400);
      }

      const lesson = await this.lessonService.createLesson({
        courseId,
        title,
        content,
        thumbnailUrl,
        illustrationUrl,
        order
      });

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };

  updateLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const lesson = await this.lessonService.updateLesson(id, req.body);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };

  deleteLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      await this.lessonService.deleteLesson(id);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  updateLessonOrder = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { courseId } = req.params;
      const { updates } = req.body;

      if (!updates || !Array.isArray(updates)) {
        throw new CustomError('Updates array is required', 400);
      }

      await this.lessonService.updateLessonOrder(courseId, updates);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        message: 'Lesson order updated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  bulkDeleteLessons = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { lessonIds } = req.body;

      if (!lessonIds || !Array.isArray(lessonIds)) {
        throw new CustomError('Lesson IDs array is required', 400);
      }

      await this.lessonService.bulkDeleteLessons(lessonIds);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        message: 'Lessons deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  duplicateLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const lesson = await this.lessonService.duplicateLesson(id);

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };
}
