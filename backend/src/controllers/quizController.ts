import { Request, Response, NextFunction } from 'express';
import { QuizService } from '../services/quizService';
import { CustomError } from '../utils/customError';
import { AuthRequest } from '../middleware/authenticate';

export class QuizController {
  private quizService: QuizService;

  constructor() {
    this.quizService = new QuizService();
  }

  getAllQuizzes = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const quizzes = await this.quizService.getAllQuizzes(req.query);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: quizzes
      });
    } catch (error) {
      next(error);
    }
  };

  getQuizByLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { lessonId } = req.params;
      const quiz = await this.quizService.getQuizByLesson(lessonId);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: quiz
      });
    } catch (error) {
      next(error);
    }
  };

  getQuizById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const quiz = await this.quizService.getQuizById(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: quiz
      });
    } catch (error) {
      next(error);
    }
  };

  createQuiz = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { lessonId, title, questions } = req.body;

      if (!lessonId || !title || !questions) {
        throw new CustomError('Missing required fields', 400);
      }

      const quiz = await this.quizService.createQuiz({
        lessonId,
        title,
        questions
      });

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: quiz
      });
    } catch (error) {
      next(error);
    }
  };

  updateQuiz = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const quiz = await this.quizService.updateQuiz(id, req.body);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: quiz
      });
    } catch (error) {
      next(error);
    }
  };

  deleteQuiz = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      await this.quizService.deleteQuiz(id);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  submitQuiz = async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      const { answers } = req.body;

      if (!userId || !answers) {
        throw new CustomError('Missing required fields', 400);
      }

      const result = await this.quizService.submitQuiz(id, userId, answers);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: result
      });
    } catch (error) {
      next(error);
    }
  };

  bulkDeleteQuizzes = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { quizIds } = req.body;

      if (!quizIds || !Array.isArray(quizIds)) {
        throw new CustomError('Quiz IDs array is required', 400);
      }

      await this.quizService.bulkDeleteQuizzes(quizIds);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        message: 'Quizzes deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  duplicateQuiz = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const quiz = await this.quizService.duplicateQuiz(id);

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: quiz
      });
    } catch (error) {
      next(error);
    }
  };
}
