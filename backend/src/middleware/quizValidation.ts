import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { CustomError } from '../utils/customError';

// Validation middleware to handle validation errors
export const handleValidationErrors = (req: Request, _res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ');
    throw new CustomError(errorMessages, 400);
  }
  next();
};

// Quiz creation validation
export const validateCreateQuiz = [
  body('lessonId')
    .isUUID()
    .withMessage('Lesson ID must be a valid UUID'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  body('questions')
    .isArray({ min: 1 })
    .withMessage('At least one question is required'),
  body('questions.*.text')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Question text is required'),
  body('questions.*.choices')
    .isArray({ min: 2 })
    .withMessage('Each question must have at least 2 choices'),
  body('questions.*.choices.*.text')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Choice text is required'),
  body('questions.*.choices.*.isCorrect')
    .isBoolean()
    .withMessage('Choice isCorrect must be a boolean'),
  // Custom validation to ensure at least one correct answer per question
  body('questions').custom((questions) => {
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      const hasCorrectAnswer = question.choices?.some((choice: any) => choice.isCorrect === true);
      if (!hasCorrectAnswer) {
        throw new Error(`Question ${i + 1} must have at least one correct answer`);
      }
    }
    return true;
  }),
  handleValidationErrors
];

// Quiz update validation
export const validateUpdateQuiz = [
  param('id')
    .isUUID()
    .withMessage('Quiz ID must be a valid UUID'),
  body('lessonId')
    .optional()
    .isUUID()
    .withMessage('Lesson ID must be a valid UUID'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  body('questions')
    .optional()
    .isArray({ min: 1 })
    .withMessage('At least one question is required'),
  body('questions.*.text')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Question text is required'),
  body('questions.*.choices')
    .optional()
    .isArray({ min: 2 })
    .withMessage('Each question must have at least 2 choices'),
  body('questions.*.choices.*.text')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Choice text is required'),
  body('questions.*.choices.*.isCorrect')
    .optional()
    .isBoolean()
    .withMessage('Choice isCorrect must be a boolean'),
  // Custom validation for questions if provided
  body('questions').optional().custom((questions) => {
    if (questions) {
      for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        const hasCorrectAnswer = question.choices?.some((choice: any) => choice.isCorrect === true);
        if (!hasCorrectAnswer) {
          throw new Error(`Question ${i + 1} must have at least one correct answer`);
        }
      }
    }
    return true;
  }),
  handleValidationErrors
];

// Quiz ID parameter validation
export const validateQuizId = [
  param('id')
    .isUUID()
    .withMessage('Quiz ID must be a valid UUID'),
  handleValidationErrors
];

// Lesson ID parameter validation
export const validateLessonId = [
  param('lessonId')
    .isUUID()
    .withMessage('Lesson ID must be a valid UUID'),
  handleValidationErrors
];

// Bulk delete validation
export const validateBulkDelete = [
  body('quizIds')
    .isArray({ min: 1 })
    .withMessage('Quiz IDs array is required and must not be empty'),
  body('quizIds.*')
    .isUUID()
    .withMessage('Each quiz ID must be a valid UUID'),
  handleValidationErrors
];

// Quiz submission validation
export const validateQuizSubmission = [
  param('id')
    .isUUID()
    .withMessage('Quiz ID must be a valid UUID'),
  body('answers')
    .isObject()
    .withMessage('Answers must be an object'),
  handleValidationErrors
];

// Query parameter validation for quiz listing
export const validateQuizQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Search term must not exceed 255 characters'),
  query('lessonId')
    .optional()
    .isUUID()
    .withMessage('Lesson ID must be a valid UUID'),
  handleValidationErrors
];
