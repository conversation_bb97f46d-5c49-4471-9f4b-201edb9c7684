import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { CustomError } from '../utils/customError';

// Validation middleware to handle validation errors
export const handleValidationErrors = (req: Request, _res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ');
    throw new CustomError(errorMessages, 400);
  }
  next();
};

// Lesson creation validation
export const validateCreateLesson = [
  body('courseId')
    .isUUID()
    .withMessage('Course ID must be a valid UUID'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  body('content')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Content is required'),
  body('thumbnailUrl')
    .optional()
    .isURL()
    .withMessage('Thumbnail URL must be a valid URL'),
  body('illustrationUrl')
    .optional()
    .isURL()
    .withMessage('Illustration URL must be a valid URL'),
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer'),
  handleValidationErrors
];

// Lesson update validation
export const validateUpdateLesson = [
  param('id')
    .isUUID()
    .withMessage('Lesson ID must be a valid UUID'),
  body('courseId')
    .optional()
    .isUUID()
    .withMessage('Course ID must be a valid UUID'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Content cannot be empty'),
  body('thumbnailUrl')
    .optional()
    .isURL()
    .withMessage('Thumbnail URL must be a valid URL'),
  body('illustrationUrl')
    .optional()
    .isURL()
    .withMessage('Illustration URL must be a valid URL'),
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer'),
  handleValidationErrors
];

// Lesson ID parameter validation
export const validateLessonId = [
  param('id')
    .isUUID()
    .withMessage('Lesson ID must be a valid UUID'),
  handleValidationErrors
];

// Course ID parameter validation
export const validateCourseId = [
  param('courseId')
    .isUUID()
    .withMessage('Course ID must be a valid UUID'),
  handleValidationErrors
];

// Lesson order update validation
export const validateLessonOrder = [
  param('courseId')
    .isUUID()
    .withMessage('Course ID must be a valid UUID'),
  body('updates')
    .isArray({ min: 1 })
    .withMessage('Updates array is required and must not be empty'),
  body('updates.*.id')
    .isUUID()
    .withMessage('Each update must have a valid lesson ID'),
  body('updates.*.order')
    .isInt({ min: 0 })
    .withMessage('Each update must have a valid order number'),
  handleValidationErrors
];

// Bulk delete validation
export const validateBulkDelete = [
  body('lessonIds')
    .isArray({ min: 1 })
    .withMessage('Lesson IDs array is required and must not be empty'),
  body('lessonIds.*')
    .isUUID()
    .withMessage('Each lesson ID must be a valid UUID'),
  handleValidationErrors
];

// Query parameter validation for lesson listing
export const validateLessonQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Search term must not exceed 255 characters'),
  query('courseId')
    .optional()
    .isUUID()
    .withMessage('Course ID must be a valid UUID'),
  handleValidationErrors
];
