import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import theme from './shared/theme'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(PrimeVue, {
    theme: {
        preset: theme,
        options: {
            darkModeSelector: false
        }
    }
})
app.use(createPinia())
app.use(router)

app.mount('#app')
