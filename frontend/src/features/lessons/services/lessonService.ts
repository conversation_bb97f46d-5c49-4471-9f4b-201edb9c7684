import api, { handleApiResponse, handleApiError } from '@/shared/lib/api';
import type { ApiResponse } from "@/shared/lib/api"
import type { AxiosResponse } from 'axios';

// Lesson types
export interface Lesson {
  id: string;
  courseId: string;
  title: string;
  content: string;
  thumbnailUrl: string | null;
  illustrationUrl: string | null;
  order: number;
  createdAt: string;
  updatedAt: string;
  course?: {
    id: string;
    title: string;
    level: string;
    status: string;
  };
}

export interface CreateLessonRequest {
  courseId: string;
  title: string;
  content: string;
  thumbnailUrl?: string;
  illustrationUrl?: string;
  order?: number;
}

export interface UpdateLessonRequest extends Partial<CreateLessonRequest> {}

export interface LessonFilters {
  search?: string;
  courseId?: string;
  page?: number;
  limit?: number;
}

export interface LessonsResponse {
  lessons: Lesson[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UpdateLessonOrderRequest {
  updates: {
    id: string;
    order: number;
  }[];
}

export interface BulkDeleteRequest {
  lessonIds: string[];
}

// Lesson Service
export class LessonService {
  /**
   * Get all lessons with optional filters
   */
  static async getLessons(filters: LessonFilters = {}): Promise<LessonsResponse> {
    try {
      const params = new URLSearchParams();

      if (filters.search) params.append('search', filters.search);
      if (filters.courseId) params.append('courseId', filters.courseId);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response: AxiosResponse<ApiResponse<LessonsResponse>> = await api.get(
        `/lessons?${params.toString()}`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get lessons by course ID
   */
  static async getLessonsByCourse(courseId: string): Promise<Lesson[]> {
    try {
      const response: AxiosResponse<ApiResponse<Lesson[]>> = await api.get(
        `/lessons/course/${courseId}/lessons`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get lesson by ID
   */
  static async getLessonById(id: string): Promise<Lesson> {
    try {
      const response: AxiosResponse<ApiResponse<Lesson>> = await api.get(
        `/lessons/${id}`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Create new lesson
   */
  static async createLesson(data: CreateLessonRequest): Promise<Lesson> {
    try {
      const response: AxiosResponse<ApiResponse<Lesson>> = await api.post(
        '/lessons',
        data
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Update lesson
   */
  static async updateLesson(id: string, data: UpdateLessonRequest): Promise<Lesson> {
    try {
      const response: AxiosResponse<ApiResponse<Lesson>> = await api.put(
        `/lessons/${id}`,
        data
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Delete lesson
   */
  static async deleteLesson(id: string): Promise<void> {
    try {
      await api.delete(`/lessons/${id}`);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Update lesson order within a course
   */
  static async updateLessonOrder(courseId: string, data: UpdateLessonOrderRequest): Promise<void> {
    try {
      await api.patch(`/lessons/course/${courseId}/order`, data);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Bulk delete lessons
   */
  static async bulkDeleteLessons(data: BulkDeleteRequest): Promise<void> {
    try {
      await api.post('/lessons/bulk/delete', data);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Duplicate lesson
   */
  static async duplicateLesson(id: string): Promise<Lesson> {
    try {
      const response: AxiosResponse<ApiResponse<Lesson>> = await api.post(
        `/lessons/${id}/duplicate`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }
}
