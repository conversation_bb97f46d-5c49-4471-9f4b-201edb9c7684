import api, { handleApiResponse, handleApiError } from '@/shared/lib/api';
import type { ApiResponse } from "@/shared/lib/api"
import type { AxiosResponse } from 'axios';

// Quiz types
export interface Choice {
  id: string;
  questionId: string;
  text: string;
  isCorrect: boolean;
}

export interface Question {
  id: string;
  quizId: string;
  text: string;
  choices: Choice[];
  createdAt: string;
  updatedAt: string;
}

export interface Quiz {
  id: string;
  lessonId: string;
  title: string;
  questions: Question[];
  questionsCount: number;
  createdAt: string;
  updatedAt: string;
  lesson?: {
    id: string;
    title: string;
    courseId: string;
  };
}

export interface CreateQuizRequest {
  lessonId: string;
  title: string;
  questions: Array<{
    text: string;
    choices: Array<{
      text: string;
      isCorrect: boolean;
    }>;
  }>;
}

export interface UpdateQuizRequest extends Partial<CreateQuizRequest> {}

export interface QuizFilters {
  search?: string;
  lessonId?: string;
  page?: number;
  limit?: number;
}

export interface QuizzesResponse {
  quizzes: Quiz[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface BulkDeleteRequest {
  quizIds: string[];
}

export interface QuizSubmissionRequest {
  answers: Record<string, string>; // questionId -> choiceId
}

export interface QuizSubmissionResult {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  xpEarned: number;
}

// Quiz Service
export class QuizService {
  /**
   * Get all quizzes with optional filters
   */
  static async getQuizzes(filters: QuizFilters = {}): Promise<QuizzesResponse> {
    try {
      const params = new URLSearchParams();

      if (filters.search) params.append('search', filters.search);
      if (filters.lessonId) params.append('lessonId', filters.lessonId);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response: AxiosResponse<ApiResponse<QuizzesResponse>> = await api.get(
        `/quizzes?${params.toString()}`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get quiz by lesson ID
   */
  static async getQuizByLesson(lessonId: string): Promise<Quiz> {
    try {
      const response: AxiosResponse<ApiResponse<Quiz>> = await api.get(
        `/quizzes/lesson/${lessonId}/quiz`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get quiz by ID
   */
  static async getQuizById(id: string): Promise<Quiz> {
    try {
      const response: AxiosResponse<ApiResponse<Quiz>> = await api.get(
        `/quizzes/${id}`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Create new quiz
   */
  static async createQuiz(data: CreateQuizRequest): Promise<Quiz> {
    try {
      const response: AxiosResponse<ApiResponse<Quiz>> = await api.post(
        '/quizzes',
        data
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Update quiz
   */
  static async updateQuiz(id: string, data: UpdateQuizRequest): Promise<Quiz> {
    try {
      const response: AxiosResponse<ApiResponse<Quiz>> = await api.put(
        `/quizzes/${id}`,
        data
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Delete quiz
   */
  static async deleteQuiz(id: string): Promise<void> {
    try {
      await api.delete(`/quizzes/${id}`);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Bulk delete quizzes
   */
  static async bulkDeleteQuizzes(data: BulkDeleteRequest): Promise<void> {
    try {
      await api.post('/quizzes/bulk/delete', data);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Duplicate quiz
   */
  static async duplicateQuiz(id: string): Promise<Quiz> {
    try {
      const response: AxiosResponse<ApiResponse<Quiz>> = await api.post(
        `/quizzes/${id}/duplicate`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Submit quiz answers
   */
  static async submitQuiz(id: string, data: QuizSubmissionRequest): Promise<QuizSubmissionResult> {
    try {
      const response: AxiosResponse<ApiResponse<QuizSubmissionResult>> = await api.post(
        `/quizzes/${id}/submit`,
        data
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }
}
