import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: () => import('../views/AdminLogin.vue'),
    },
    {
      path: '/admin/dashboard',
      name: 'admin-dashboard',
      component: () => import('../views/AdminDashboard.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
      children: [
        {
          path: 'courses',
          name: 'admin-courses',
          component: () => import('../views/AdminCourses.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'lessons',
          name: 'admin-lessons',
          component: () => import('../views/AdminLessons.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        },
        {
          path: 'quizzes',
          name: 'admin-quizzes',
          component: () => import('../views/AdminQuizzes.vue'),
          meta: { requiresAuth: true, requiresAdmin: true },
        }
      ]
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
  ],
})

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'admin-login', query: { redirect: to.fullPath } })
  } else if (to.meta.requiresAdmin && !authStore.isAdmin) {
    next({ name: 'admin-login' })
  } else {
    next()
  }
})

export default router
