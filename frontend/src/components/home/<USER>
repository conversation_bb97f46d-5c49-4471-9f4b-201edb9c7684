<template>
    <div class="w-full h-full flex items-center justify-center">
        <div>
            <!-- <div class="text-9xl font-bold">
                Learning traffic rules made
            </div> -->
            <div class="h-50 overflow-hidden text-9xl bg-red-200">
                <motion.div :initial="{ y: 200 }" :animate="{ y: 0 }" :exit="{ y: -200 }" :transition="{
                    duration: 1,
                    delay: 0.2,
                    type: 'spring',
                    damping: 10,
                    repeat: Infinity,
                    onRepeat: onAnimationRepeat,
                }">
                    {{ currentWord }}
                </motion.div>
            </div>
            <!-- <div class="flex items-center text-2xl">
                <h2 class="font-bold text-center text-primary">
                    Uruhushya.rw
                </h2>
                <h2>
                    agateganyo
                </h2>
            </div> -->
        </div>
    </div>
</template>
<script setup lang="ts">
import { motion } from 'motion-v';
import { computed, ref } from 'vue';

const currentWordHelper = ref(0)

const words = ['easy', 'fun', 'engaging', 'interactive', 'effective'];
const currentWord = computed(() => words[currentWordHelper.value]);

const onAnimationRepeat = () => {
    currentWordHelper.value = (currentWordHelper.value + 1) % words.length;
};

</script>
<style scoped></style>