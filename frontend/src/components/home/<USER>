<template>
    <div class="w-full h-full flex items-center justify-center">
        <div class="flex flex-col items-center">
            <div class="text-9xl">
                <div class="h-40">
                    <p>Learning traffic rules</p>
                </div>
                <div class="h-40 flex">
                    <span>made</span>
                    <div class="h-full overflow-hidden px-4">
                        <motion.div :key="currentIndexHelper" :initial="{ y: 200 }" :animate="{ y: 0 }"
                            :exit="{ y: -200 }" :transition="{
                                duration: 0.5,
                                delay: 0.1,
                                type: 'spring',
                                damping: 10,
                                onComplete: onAnimationComplete,
                            }" :class="[currentColor, 'w-auto']">
                            {{ currentWord }}
                        </motion.div>
                    </div>
                </div>
            </div>
            <div class="mt-10">
                <h1>Learn on your pace every day.</h1>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { motion } from 'motion-v';
import { computed, ref } from 'vue';

const currentIndexHelper = ref(0);
const isAnimating = ref(false);

const words = ['easy', 'fun', 'engaging', 'effective'];
const currentWord = computed(() => words[currentIndexHelper.value]);

const colors = ["text-blue-600", 'text-green-300', 'text-red-300', 'text-purple-300'];
const currentColor = computed(() => colors[currentIndexHelper.value]);

const onAnimationComplete = () => {
    isAnimating.value = false;

    // Wait 2 seconds before changing to the next word
    setTimeout(() => {
        if (!isAnimating.value) {
            isAnimating.value = true;
            currentIndexHelper.value = (currentIndexHelper.value + 1) % words.length;
            console.log('Changed to next word:', words[currentIndexHelper.value]);
        }
    }, 1000);
};

setTimeout(() => {
    isAnimating.value = true;
}, 100);

</script>
<style scoped></style>