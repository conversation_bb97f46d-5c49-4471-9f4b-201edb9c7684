<template>
    <div class="w-full h-full flex items-center justify-center">
        <div>
            <div class="text-9xl font-bold">
                Learning traffic rules made
            </div>
            <div class="h-50 overflow-hidden text-9xl bg-red-200">
                <motion.div
                    :key="currentWordHelper"
                    :initial="{ y: 200 }"
                    :animate="{ y: 0 }"
                    :exit="{ y: -200 }"
                    :transition="{
                        duration: 1,
                        delay: 0.2,
                        type: 'spring',
                        damping: 10,
                        onComplete: onAnimationComplete,
                    }"
                >
                    {{ currentWord }}
                </motion.div>
            </div>
            <!-- <div class="flex items-center text-2xl">
                <h2 class="font-bold text-center text-primary">
                    Uruhushya.rw
                </h2>
                <h2>
                    agateganyo
                </h2>
            </div> -->
        </div>
    </div>
</template>
<script setup lang="ts">
import { motion } from 'motion-v';
import { computed, ref } from 'vue';

const currentWordHelper = ref(0);
const isAnimating = ref(false);

const words = ['easy', 'fun', 'engaging', 'interactive', 'effective'];
const currentWord = computed(() => words[currentWordHelper.value]);

const onAnimationComplete = () => {
    console.log('Animation completed for word:', currentWord.value);
    isAnimating.value = false;

    // Wait 2 seconds before changing to the next word
    setTimeout(() => {
        if (!isAnimating.value) {
            isAnimating.value = true;
            currentWordHelper.value = (currentWordHelper.value + 1) % words.length;
            console.log('Changed to next word:', words[currentWordHelper.value]);
        }
    }, 2000);
};

// Start the first animation
setTimeout(() => {
    isAnimating.value = true;
}, 100);

</script>
<style scoped></style>