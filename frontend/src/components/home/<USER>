<template>
    <div class="w-full h-full flex items-center justify-center">
        <div>
            <div class="flex items-center text-2xl">
                <h2 class="font-bold text-center text-primary">
                    Uruhushya.rw
                </h2>
                <h2>
                    agateganyo
                </h2>
            </div>
            <div class="animated-text-container">
                <ul class="v-slides">
                    <li class="v-slide">Websites</li>
                    <li class="v-slide">Plugins</li>
                    <li class="v-slide">Web Apps</li>
                    <li class="v-slide">Portals</li>
                    <li class="v-slide">Communities</li>
                    <li class="v-slide">Digital Marketing</li>
                    <li class="v-slide">Websites</li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
</script>
<style scoped>
.container {
  padding: 2em;
}
.v-slider-frame {
  border: 1px solid #4BB3FD;
  height: 50px;
  overflow: hidden;
  text-align: center;
}
ul.v-slides {
  list-style-type: none;
  transform: translateY(50px);
  padding:0;
}
.v-slide {
  font-size: 24px;
  line-height: 50px;
  color: #4BB3FD;
}</style>