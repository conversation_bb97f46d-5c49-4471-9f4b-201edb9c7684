import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import {
  QuizService,
  type Quiz,
  type CreateQuizRequest,
  type UpdateQuizRequest,
  type QuizFilters,
  type BulkDeleteRequest,
  type QuizSubmissionRequest,
  type QuizSubmissionResult
} from '@/features/quizzes/services/quizService';

export const useQuizzesStore = defineStore('quizzes', () => {
  // State
  const quizzes = ref<Quiz[]>([]);
  const currentQuiz = ref<Quiz | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const pagination = ref({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  // Filters
  const filters = ref<QuizFilters>({
    search: '',
    lessonId: '',
    page: 1,
    limit: 10
  });

  // Computed
  const totalQuizzes = computed(() => quizzes.value.length);

  const getQuizzesByLesson = computed(() => (lessonId: string) =>
    quizzes.value.filter(quiz => quiz.lessonId === lessonId)
  );

  // Actions
  const fetchQuizzes = async (newFilters?: Partial<QuizFilters>) => {
    try {
      isLoading.value = true;
      error.value = null;

      if (newFilters) {
        filters.value = { ...filters.value, ...newFilters };
      }

      const response = await QuizService.getQuizzes(filters.value);

      // Merge new quizzes with existing ones, avoiding duplicates
      const existingIds = new Set(quizzes.value.map(quiz => quiz.id));
      const newQuizzes = response.quizzes.filter(quiz => !existingIds.has(quiz.id));

      if (filters.value.page === 1) {
        // If it's the first page, replace the array
        quizzes.value = response.quizzes;
      } else {
        // If it's subsequent pages, merge with existing
        quizzes.value = [...quizzes.value, ...newQuizzes];
      }

      pagination.value = {
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch quizzes';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchQuizByLesson = async (lessonId: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const quiz = await QuizService.getQuizByLesson(lessonId);

      // Merge quiz into the quizzes array
      const index = quizzes.value.findIndex(q => q.id === quiz.id);
      if (index !== -1) {
        // Update existing quiz
        quizzes.value[index] = { ...quizzes.value[index], ...quiz };
      } else {
        // Add new quiz
        quizzes.value.unshift(quiz);
      }

      return quiz;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch quiz for lesson';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchQuizById = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const quiz = await QuizService.getQuizById(id);
      currentQuiz.value = quiz;

      const index = quizzes.value.findIndex(q => q.id === id);
      let quizWithAllInfo = quiz;
      if (index !== -1) {
        quizzes.value[index] = { ...quizzes.value[index], ...quiz };
        quizWithAllInfo = quizzes.value[index];
      } else {
        quizzes.value.unshift(quiz);
      }

      return quizWithAllInfo;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch quiz';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createQuiz = async (data: CreateQuizRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const newQuiz = await QuizService.createQuiz(data);

      // Add to quizzes array
      quizzes.value.unshift(newQuiz);

      // Update pagination total
      pagination.value.total += 1;

      return newQuiz;
    } catch (err: any) {
      error.value = err.message || 'Failed to create quiz';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateQuiz = async (id: string, data: UpdateQuizRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const updatedQuiz = await QuizService.updateQuiz(id, data);

      // Merge updated quiz into the quizzes array
      const index = quizzes.value.findIndex(quiz => quiz.id === id);
      if (index !== -1) {
        quizzes.value[index] = { ...quizzes.value[index], ...updatedQuiz };
      } else {
        // Add if not found (shouldn't happen, but safety measure)
        quizzes.value.unshift(updatedQuiz);
      }

      // Update current quiz if it's the same
      if (currentQuiz.value?.id === id) {
        currentQuiz.value = { ...currentQuiz.value, ...updatedQuiz };
      }

      return updatedQuiz;
    } catch (err: any) {
      error.value = err.message || 'Failed to update quiz';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteQuiz = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      await QuizService.deleteQuiz(id);

      // Remove from quizzes array
      quizzes.value = quizzes.value.filter(quiz => quiz.id !== id);

      // Update pagination total
      pagination.value.total -= 1;

      // Clear current quiz if it's the deleted one
      if (currentQuiz.value?.id === id) {
        currentQuiz.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete quiz';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkDeleteQuizzes = async (data: BulkDeleteRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      await QuizService.bulkDeleteQuizzes(data);

      // Remove deleted quizzes from array
      quizzes.value = quizzes.value.filter(quiz => !data.quizIds.includes(quiz.id));

      // Update pagination total
      pagination.value.total -= data.quizIds.length;

      // Clear current quiz if it was deleted
      if (currentQuiz.value && data.quizIds.includes(currentQuiz.value.id)) {
        currentQuiz.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete quizzes';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const duplicateQuiz = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const duplicatedQuiz = await QuizService.duplicateQuiz(id);

      // Add duplicated quiz to array
      quizzes.value.unshift(duplicatedQuiz);

      // Update pagination total
      pagination.value.total += 1;

      return duplicatedQuiz;
    } catch (err: any) {
      error.value = err.message || 'Failed to duplicate quiz';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const submitQuiz = async (id: string, data: QuizSubmissionRequest): Promise<QuizSubmissionResult> => {
    try {
      isLoading.value = true;
      error.value = null;

      const result = await QuizService.submitQuiz(id, data);
      return result;
    } catch (err: any) {
      error.value = err.message || 'Failed to submit quiz';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchQuizzes = async (searchTerm: string) => {
    await fetchQuizzes({ search: searchTerm, page: 1 });
  };

  const filterByLesson = async (lessonId: string) => {
    await fetchQuizzes({ lessonId, page: 1 });
  };

  const changePage = async (page: number) => {
    await fetchQuizzes({ page });
  };

  const clearError = () => {
    error.value = null;
  };

  const clearCurrentQuiz = () => {
    currentQuiz.value = null;
  };

  const resetFilters = async () => {
    filters.value = {
      search: '',
      lessonId: '',
      page: 1,
      limit: 10
    };
    await fetchQuizzes();
  };

  return {
    // State
    quizzes,
    currentQuiz,
    isLoading,
    error,
    pagination,
    filters,

    // Computed
    totalQuizzes,
    getQuizzesByLesson,

    // Actions
    fetchQuizzes,
    fetchQuizByLesson,
    fetchQuizById,
    createQuiz,
    updateQuiz,
    deleteQuiz,
    bulkDeleteQuizzes,
    duplicateQuiz,
    submitQuiz,
    searchQuizzes,
    filterByLesson,
    changePage,
    clearError,
    clearCurrentQuiz,
    resetFilters
  };
});
