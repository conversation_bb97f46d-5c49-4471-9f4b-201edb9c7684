import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { 
  LessonService, 
  type Lesson, 
  type CreateLessonRequest, 
  type UpdateLessonRequest,
  type LessonFilters,
  type UpdateLessonOrderRequest,
  type BulkDeleteRequest
} from '@/features/lessons/services/lessonService';

export const useLessonsStore = defineStore('lessons', () => {
  // State
  const lessons = ref<Lesson[]>([]);
  const currentLesson = ref<Lesson | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const pagination = ref({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  // Filters
  const filters = ref<LessonFilters>({
    search: '',
    courseId: '',
    page: 1,
    limit: 10
  });

  // Computed
  const totalLessons = computed(() => lessons.value.length);

  const getLessonsByCourse = computed(() => (courseId: string) => 
    lessons.value.filter(lesson => lesson.courseId === courseId)
  );

  // Actions
  const fetchLessons = async (newFilters?: Partial<LessonFilters>) => {
    try {
      isLoading.value = true;
      error.value = null;

      if (newFilters) {
        filters.value = { ...filters.value, ...newFilters };
      }

      const response = await LessonService.getLessons(filters.value);
      
      lessons.value = response.lessons;
      pagination.value = {
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch lessons';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchLessonsByCourse = async (courseId: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const courseLessons = await LessonService.getLessonsByCourse(courseId);
      
      // Update lessons array with course lessons
      const otherLessons = lessons.value.filter(lesson => lesson.courseId !== courseId);
      lessons.value = [...otherLessons, ...courseLessons];
      
      return courseLessons;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch course lessons';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchLessonById = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const lesson = await LessonService.getLessonById(id);
      currentLesson.value = lesson;

      // Update lesson in the lessons array if it exists
      const index = lessons.value.findIndex(l => l.id === id);
      if (index !== -1) {
        lessons.value[index] = lesson;
      }

      return lesson;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch lesson';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createLesson = async (data: CreateLessonRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const newLesson = await LessonService.createLesson(data);
      
      // Add to lessons array
      lessons.value.unshift(newLesson);
      
      // Update pagination total
      pagination.value.total += 1;

      return newLesson;
    } catch (err: any) {
      error.value = err.message || 'Failed to create lesson';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateLesson = async (id: string, data: UpdateLessonRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const updatedLesson = await LessonService.updateLesson(id, data);
      
      // Update lesson in the lessons array
      const index = lessons.value.findIndex(lesson => lesson.id === id);
      if (index !== -1) {
        lessons.value[index] = updatedLesson;
      }

      // Update current lesson if it's the same
      if (currentLesson.value?.id === id) {
        currentLesson.value = updatedLesson;
      }

      return updatedLesson;
    } catch (err: any) {
      error.value = err.message || 'Failed to update lesson';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteLesson = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      await LessonService.deleteLesson(id);
      
      // Remove from lessons array
      lessons.value = lessons.value.filter(lesson => lesson.id !== id);
      
      // Update pagination total
      pagination.value.total -= 1;

      // Clear current lesson if it's the deleted one
      if (currentLesson.value?.id === id) {
        currentLesson.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete lesson';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateLessonOrder = async (courseId: string, data: UpdateLessonOrderRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      await LessonService.updateLessonOrder(courseId, data);
      
      // Update local lesson orders
      data.updates.forEach(update => {
        const lesson = lessons.value.find(l => l.id === update.id);
        if (lesson) {
          lesson.order = update.order;
        }
      });

      // Re-sort lessons by order
      lessons.value.sort((a, b) => a.order - b.order);
    } catch (err: any) {
      error.value = err.message || 'Failed to update lesson order';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const bulkDeleteLessons = async (data: BulkDeleteRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      await LessonService.bulkDeleteLessons(data);
      
      // Remove deleted lessons from array
      lessons.value = lessons.value.filter(lesson => !data.lessonIds.includes(lesson.id));
      
      // Update pagination total
      pagination.value.total -= data.lessonIds.length;

      // Clear current lesson if it was deleted
      if (currentLesson.value && data.lessonIds.includes(currentLesson.value.id)) {
        currentLesson.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete lessons';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const duplicateLesson = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const duplicatedLesson = await LessonService.duplicateLesson(id);
      
      // Add duplicated lesson to array
      lessons.value.unshift(duplicatedLesson);
      
      // Update pagination total
      pagination.value.total += 1;

      return duplicatedLesson;
    } catch (err: any) {
      error.value = err.message || 'Failed to duplicate lesson';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const searchLessons = async (searchTerm: string) => {
    await fetchLessons({ search: searchTerm, page: 1 });
  };

  const filterByCourse = async (courseId: string) => {
    await fetchLessons({ courseId, page: 1 });
  };

  const changePage = async (page: number) => {
    await fetchLessons({ page });
  };

  const clearError = () => {
    error.value = null;
  };

  const clearCurrentLesson = () => {
    currentLesson.value = null;
  };

  const resetFilters = async () => {
    filters.value = {
      search: '',
      courseId: '',
      page: 1,
      limit: 10
    };
    await fetchLessons();
  };

  return {
    // State
    lessons,
    currentLesson,
    isLoading,
    error,
    pagination,
    filters,
    
    // Computed
    totalLessons,
    getLessonsByCourse,
    
    // Actions
    fetchLessons,
    fetchLessonsByCourse,
    fetchLessonById,
    createLesson,
    updateLesson,
    deleteLesson,
    updateLessonOrder,
    bulkDeleteLessons,
    duplicateLesson,
    searchLessons,
    filterByCourse,
    changePage,
    clearError,
    clearCurrentLesson,
    resetFilters
  };
});
