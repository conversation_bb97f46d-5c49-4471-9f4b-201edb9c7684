<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Quizzes Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage lesson quizzes
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <div class="flex gap-4 items-center">
          <IconField>
            <InputIcon class="pi pi-search" />
            <InputText v-model="searchKey" placeholder="Search quizzes..." class="w-full pl-10" />
          </IconField>
          <Dropdown v-model="selectedLessonFilter" :options="lessonOptions" option-label="label" option-value="value"
            placeholder="Filter by lesson" class="w-64" show-clear />
        </div>
      </template>
      <template #end>
        <div class="flex gap-2">
          <Button icon="pi pi-trash" severity="danger" outlined @click="bulkDelete" v-tooltip.left="'Delete Selected'"
            :disabled="!selectedQuizzes.length" label="Delete Selected" />
          <Button icon="pi pi-plus" @click="openCreateDialog" v-tooltip.left="'Create New Quiz'"
            aria-label="Create New Quiz" label="Create new quiz" />
        </div>
      </template>
    </Menubar>
    <Divider />
    <DataTable v-model:selection="selectedQuizzes" :value="quizzesStore.quizzes" :loading="quizzesStore.isLoading"
      paginator :rows="quizzesStore.pagination.limit" :totalRecords="quizzesStore.pagination.total"
      :rowsPerPageOptions="[5, 10, 20, 50]" :first="(quizzesStore.pagination.page - 1) * quizzesStore.pagination.limit"
      @page="onPageChange" tableStyle="min-width: 50rem" :lazy="true" dataKey="id" responsiveLayout="scroll"
      selection-mode="multiple">
      <Column selection-mode="multiple" header-style="width: 3rem"></Column>
      <Column field="title" header="Quiz Title" style="width: 35%"></Column>
      <Column field="lesson.title" header="Lesson" style="width: 25%">
        <template #body="{ data }">
          <p>{{ data.lesson?.title || 'Unknown' }}</p>
        </template>
      </Column>
      <Column field="questionsCount" header="Questions" style="width: 10%">
        <template #body="{ data }">
          <Badge :value="data.questionsCount || 0" />
        </template>
      </Column>
      <Column field="createdAt" header="Created" style="width: 15%">
        <template #body="{ data }">
          {{ formatDate(data.createdAt) }}
        </template>
      </Column>
      <Column header="Actions" style="width: 15%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button icon="pi pi-eye" size="small" severity="info" outlined @click="viewQuiz(data.id)"
              v-tooltip.top="'View Quiz'" />
            <Button icon="pi pi-pencil" size="small" severity="secondary" outlined @click="editQuiz(data.id)"
              v-tooltip.top="'Edit Quiz'" />
            <Button icon="pi pi-copy" size="small" severity="help" outlined @click="duplicateQuiz(data.id)"
              v-tooltip.top="'Duplicate Quiz'" />
            <Button icon="pi pi-trash" size="small" severity="danger" outlined @click="deleteQuiz(data.id)"
              v-tooltip.top="'Delete Quiz'" />
          </div>
        </template>
      </Column>
    </DataTable>
  </div>

  <!-- Create/Edit Dialog -->
  <Dialog v-model:visible="showFormDialog" modal :header="isEditMode ? 'Edit Quiz' : 'Create New Quiz'"
    :style="{ width: '80rem' }" :breakpoints="{ '1199px': '90vw', '575px': '95vw' }" :draggable="false"
    :closable="true">
    <form @submit.prevent="isEditMode ? updateQuiz() : createQuiz()" class="space-y-6">
      <div class="grid grid-cols-1 gap-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="quizLesson" class="block text-sm font-medium mb-2">Lesson *</label>
            <Dropdown id="quizLesson" v-model="formData.lessonId" :options="lessonOptions" option-label="label"
              option-value="value" class="w-full" placeholder="Select lesson" :invalid="!!formErrors.lessonId"
              required />
            <small v-if="formErrors.lessonId" class="text-red-500">{{ formErrors.lessonId }}</small>
          </div>

          <div>
            <label for="quizTitle" class="block text-sm font-medium mb-2">Quiz Title *</label>
            <InputText id="quizTitle" v-model="formData.title" class="w-full" placeholder="Enter quiz title"
              :invalid="!!formErrors.title" required />
            <small v-if="formErrors.title" class="text-red-500">{{ formErrors.title }}</small>
          </div>
        </div>

        <!-- Questions Section -->
        <div>
          <div class="flex justify-between items-center mb-4">
            <label class="block text-sm font-medium">Questions *</label>
            <Button type="button" icon="pi pi-plus" label="Add Question" size="small" @click="addQuestion" />
          </div>

          <div v-if="formData.questions.length === 0"
            class="text-center py-8 border-2 border-dashed border-surface-300 rounded-lg">
            <i class="pi pi-question-circle text-4xl text-surface-400 mb-4"></i>
            <p class="text-surface-600 dark:text-surface-400 mb-4">No questions added yet</p>
            <Button type="button" label="Add First Question" icon="pi pi-plus" @click="addQuestion" />
          </div>

          <div v-else class="space-y-4">
            <Card v-for="(question, qIndex) in formData.questions" :key="qIndex" class="relative">
              <template #header>
                <div class="flex justify-between items-center p-4 pb-0">
                  <h4 class="font-semibold">Question {{ qIndex + 1 }}</h4>
                  <Button type="button" icon="pi pi-trash" severity="danger" outlined size="small"
                    @click="removeQuestion(qIndex)" v-tooltip.left="'Remove Question'" />
                </div>
              </template>
              <template #content>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium mb-2">Question Text *</label>
                    <Textarea v-model="question.text" class="w-full" rows="3" placeholder="Enter question text"
                      required />
                  </div>

                  <div>
                    <div class="flex justify-between items-center mb-2">
                      <label class="block text-sm font-medium">Answer Choices *</label>
                      <Button type="button" icon="pi pi-plus" label="Add Choice" size="small" outlined
                        @click="addChoice(qIndex)" />
                    </div>

                    <div class="space-y-2">
                      <div v-for="(choice, cIndex) in question.choices" :key="cIndex" class="flex gap-2 items-center">
                        <Checkbox v-model="choice.isCorrect" :binary="true" :input-id="`choice-${qIndex}-${cIndex}`" />
                        <InputText v-model="choice.text" class="flex-1" placeholder="Enter choice text" required />
                        <Button type="button" icon="pi pi-trash" severity="danger" outlined size="small"
                          @click="removeChoice(qIndex, cIndex)" :disabled="question.choices.length <= 2"
                          v-tooltip.left="'Remove Choice'" />
                      </div>
                    </div>
                    <small class="text-surface-500">Check the box next to correct answers</small>
                  </div>
                </div>
              </template>
            </Card>
          </div>

          <small v-if="formErrors.questions" class="text-red-500">{{ formErrors.questions }}</small>
        </div>
      </div>

      <div class="flex justify-end gap-3 pt-4">
        <Button type="button" label="Cancel" severity="secondary" outlined @click="closeFormDialog" />
        <Button type="submit" :label="isEditMode ? 'Update Quiz' : 'Create Quiz'" :loading="isCreating || isUpdating"
          :disabled="isCreating || isUpdating" />
      </div>
    </form>
  </Dialog>

  <!-- View Dialog -->
  <Dialog v-model:visible="showViewDialog" modal header="Quiz Details" :style="{ width: '70rem' }"
    :breakpoints="{ '1199px': '85vw', '575px': '95vw' }" :draggable="false" :closable="true">
    <div v-if="selectedQuiz" class="space-y-6">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Lesson</h3>
          <Badge :value="selectedQuiz.lesson?.title || 'Unknown'" severity="info" />
        </div>
        <div>
          <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Questions Count</h3>
          <Badge :value="selectedQuiz.questions.length || 0" />
        </div>
      </div>

      <div>
        <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Title</h3>
        <p class="text-surface-700 dark:text-surface-300">{{ selectedQuiz.title }}</p>
      </div>

      <div v-if="selectedQuiz.questions && selectedQuiz.questions.length > 0">
        <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-4">Questions</h3>
        <div class="space-y-4">
          <Card v-for="(question, index) in selectedQuiz.questions" :key="question.id">
            <template #header>
              <div class="p-4 pb-0">
                <h4 class="font-semibold">Question {{ index + 1 }}</h4>
              </div>
            </template>
            <template #content>
              <div class="space-y-3">
                <p class="text-surface-700 dark:text-surface-300">{{ question.text }}</p>
                <div class="space-y-2">
                  <div v-for="choice in question.choices" :key="choice.id" class="flex items-center gap-2">
                    <i
                      :class="choice.isCorrect ? 'pi pi-check-circle text-green-500' : 'pi pi-circle text-surface-400'"></i>
                    <span
                      :class="choice.isCorrect ? 'font-semibold text-green-700 dark:text-green-400' : 'text-surface-700 dark:text-surface-300'">
                      {{ choice.text }}
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>

      <div class="flex justify-end gap-3 pt-4">
        <Button label="Edit" icon="pi pi-pencil" @click="openEditDialog" />
        <Button label="Close" severity="secondary" outlined @click="showViewDialog = false" />
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useQuizzesStore } from '@/stores/quizzes';
import { useLessonsStore } from '@/stores/lessons';
import type { Quiz } from '@/features/quizzes/services/quizService';

// PrimeVue components
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import Badge from 'primevue/badge';
import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import Card from 'primevue/card';
import Checkbox from 'primevue/checkbox';

// Stores
const quizzesStore = useQuizzesStore();
const lessonsStore = useLessonsStore();

// Reactive state
const searchKey = ref('');
const selectedLessonFilter = ref('');
const selectedQuizzes = ref<Quiz[]>([]);
const selectedQuiz = ref<Quiz | null>(null);
const showFormDialog = ref(false);
const showViewDialog = ref(false);
const isEditMode = ref(false);
const isCreating = ref(false);
const isUpdating = ref(false);

// Form data
const formData = reactive({
  id: '',
  lessonId: '',
  title: '',
  questions: [] as Array<{
    text: string;
    choices: Array<{
      text: string;
      isCorrect: boolean;
    }>;
  }>
});

// Form errors
const formErrors = reactive({
  lessonId: '',
  title: '',
  questions: ''
});

// Computed
const lessonOptions = computed(() => [
  { label: 'All Lessons', value: '' },
  ...lessonsStore.lessons.map(lesson => ({
    label: `${lesson.course?.title || 'Unknown Course'} - ${lesson.title}`,
    value: lesson.id
  }))
]);

// Watchers
watch(searchKey, (newValue) => {
  if (newValue.length >= 3 || newValue.length === 0) {
    quizzesStore.searchQuizzes(newValue);
  }
});

watch(selectedLessonFilter, (newValue) => {
  quizzesStore.filterByLesson(newValue);
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const onPageChange = (event: any) => {
  quizzesStore.changePage(event.page + 1);
};

const resetForm = () => {
  formData.id = '';
  formData.lessonId = '';
  formData.title = '';
  formData.questions = [];

  formErrors.lessonId = '';
  formErrors.title = '';
  formErrors.questions = '';
};

const validateForm = () => {
  let isValid = true;

  // Reset errors
  formErrors.lessonId = '';
  formErrors.title = '';
  formErrors.questions = '';

  if (!formData.lessonId) {
    formErrors.lessonId = 'Lesson is required';
    isValid = false;
  }

  if (!formData.title.trim()) {
    formErrors.title = 'Title is required';
    isValid = false;
  }

  if (formData.questions.length === 0) {
    formErrors.questions = 'At least one question is required';
    isValid = false;
  }

  // Validate each question
  for (let i = 0; i < formData.questions.length; i++) {
    const question = formData.questions[i];
    if (!question.text.trim()) {
      formErrors.questions = `Question ${i + 1} text is required`;
      isValid = false;
      break;
    }

    if (question.choices.length < 2) {
      formErrors.questions = `Question ${i + 1} must have at least 2 choices`;
      isValid = false;
      break;
    }

    const hasCorrectAnswer = question.choices.some(choice => choice.isCorrect);
    if (!hasCorrectAnswer) {
      formErrors.questions = `Question ${i + 1} must have at least one correct answer`;
      isValid = false;
      break;
    }

    for (let j = 0; j < question.choices.length; j++) {
      if (!question.choices[j].text.trim()) {
        formErrors.questions = `Question ${i + 1}, choice ${j + 1} text is required`;
        isValid = false;
        break;
      }
    }

    if (!isValid) break;
  }

  return isValid;
};

const addQuestion = () => {
  formData.questions.push({
    text: '',
    choices: [
      { text: '', isCorrect: false },
      { text: '', isCorrect: false }
    ]
  });
};

const removeQuestion = (index: number) => {
  formData.questions.splice(index, 1);
};

const addChoice = (questionIndex: number) => {
  formData.questions[questionIndex].choices.push({
    text: '',
    isCorrect: false
  });
};

const removeChoice = (questionIndex: number, choiceIndex: number) => {
  if (formData.questions[questionIndex].choices.length > 2) {
    formData.questions[questionIndex].choices.splice(choiceIndex, 1);
  }
};

const openCreateDialog = () => {
  resetForm();
  isEditMode.value = false;
  showFormDialog.value = true;
};

const closeFormDialog = () => {
  showFormDialog.value = false;
  resetForm();
  selectedQuiz.value = null;
  isEditMode.value = false;
};

const viewQuiz = async (quizId: string) => {
  try {
    const quiz = await quizzesStore.fetchQuizById(quizId);
    selectedQuiz.value = quiz;
    showViewDialog.value = true;
  } catch (error) {
    console.error('Error fetching quiz:', error);
  }
};

const editQuiz = async (quizId: string) => {
  try {
    const quiz = await quizzesStore.fetchQuizById(quizId);
    selectedQuiz.value = quiz;

    // Set edit mode and populate form
    isEditMode.value = true;
    formData.id = quiz.id;
    formData.lessonId = quiz.lessonId;
    formData.title = quiz.title;
    formData.questions = quiz.questions.map(question => ({
      text: question.text,
      choices: question.choices.map(choice => ({
        text: choice.text,
        isCorrect: choice.isCorrect
      }))
    }));

    showFormDialog.value = true;
  } catch (error) {
    console.error('Error fetching quiz:', error);
  }
};

const createQuiz = async () => {
  if (!validateForm()) {
    return;
  }

  isCreating.value = true;

  try {
    await quizzesStore.createQuiz({
      lessonId: formData.lessonId,
      title: formData.title,
      questions: formData.questions
    });

    closeFormDialog();
  } catch (error) {
    console.error('Error creating quiz:', error);
  } finally {
    isCreating.value = false;
  }
};

const updateQuiz = async () => {
  if (!validateForm()) {
    return;
  }

  isUpdating.value = true;

  try {
    await quizzesStore.updateQuiz(formData.id, {
      lessonId: formData.lessonId,
      title: formData.title,
      questions: formData.questions
    });

    closeFormDialog();
  } catch (error) {
    console.error('Error updating quiz:', error);
  } finally {
    isUpdating.value = false;
  }
};

const deleteQuiz = async (quizId: string) => {
  if (confirm('Are you sure you want to delete this quiz?')) {
    try {
      await quizzesStore.deleteQuiz(quizId);
    } catch (error) {
      console.error('Error deleting quiz:', error);
    }
  }
};

const duplicateQuiz = async (quizId: string) => {
  try {
    await quizzesStore.duplicateQuiz(quizId);
  } catch (error) {
    console.error('Error duplicating quiz:', error);
  }
};

const bulkDelete = async () => {
  if (selectedQuizzes.value.length === 0) {
    return;
  }

  const quizIds = selectedQuizzes.value.map(quiz => quiz.id);

  if (confirm(`Are you sure you want to delete ${quizIds.length} quiz(s)?`)) {
    try {
      await quizzesStore.bulkDeleteQuizzes({ quizIds });
      selectedQuizzes.value = [];
    } catch (error) {
      console.error('Error bulk deleting quizzes:', error);
    }
  }
};

const openEditDialog = () => {
  showViewDialog.value = false;

  if (selectedQuiz.value) {
    isEditMode.value = true;
    formData.id = selectedQuiz.value.id;
    formData.lessonId = selectedQuiz.value.lessonId;
    formData.title = selectedQuiz.value.title;
    formData.questions = selectedQuiz.value.questions.map(question => ({
      text: question.text,
      choices: question.choices.map(choice => ({
        text: choice.text,
        isCorrect: choice.isCorrect
      }))
    }));
  }

  showFormDialog.value = true;
};

// Handle query parameters for navigation from lesson view
const handleQueryParams = async () => {
  const route = useRoute();
  const { lessonId, quizId, action } = route.query;

  if (lessonId && action === 'create') {
    // Pre-fill lesson in create form
    selectedLessonFilter.value = lessonId as string;
    await quizzesStore.filterByLesson(lessonId as string);

    // Open create dialog with pre-selected lesson
    formData.lessonId = lessonId as string;
    openCreateDialog();
  } else if (quizId && (action === 'view' || action === 'edit')) {
    // Load specific quiz
    try {
      const quiz = await quizzesStore.fetchQuizById(quizId as string);
      if (action === 'view') {
        selectedQuiz.value = quiz;
        showViewDialog.value = true;
      } else if (action === 'edit') {
        await editQuiz(quizId as string);
      }
    } catch (error) {
      console.error('Error handling quiz query params:', error);
    }
  }
};

// Lifecycle
onMounted(async () => {
  await Promise.all([
    quizzesStore.fetchQuizzes(),
    lessonsStore.fetchLessons()
  ]);

  // Handle query parameters after data is loaded
  await handleQueryParams();
});
</script>
