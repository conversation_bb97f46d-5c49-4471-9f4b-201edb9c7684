<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText v-model="searchKey" placeholder="Search courses..." class="w-full pl-10" />
        </IconField>
      </template>
      <template #end>
        <Button icon="pi pi-plus" @click="openCreateDialog" v-tooltip.left="'Create New Course'"
          aria-label="Create New Course" label="Create new course" />
      </template>
    </Menubar>
    <Divider />
    <DataTable :value="coursesStore.courses" :loading="coursesStore.isLoading" paginator
      :rows="coursesStore.pagination.limit" :totalRecords="coursesStore.pagination.total"
      :rowsPerPageOptions="[5, 10, 20, 50]" :first="(coursesStore.pagination.page - 1) * coursesStore.pagination.limit"
      @page="onPageChange" tableStyle="min-width: 50rem" :lazy="true" dataKey="id" responsiveLayout="scroll">
      <Column field="title" header="Course Name" style="width: 25%"></Column>
      <Column field="lessonsCount" header="Lessons" style="width: 15%"></Column>
      <Column field="duration" header="Duration (hrs)" style="width: 15%"></Column>
      <Column field="level" header="Level" style="width: 15%">
        <template #body="{ data }">
          <Badge :value="data.level"
            :severity="data.level === 'beginner' ? 'success' : data.level === 'intermediate' ? 'warning' : 'danger'" />
        </template>
      </Column>
      <Column field="status" header="Status" style="width: 15%">
        <template #body="{ data }">
          <Badge :value="data.status"
            :severity="data.status === 'published' ? 'success' : data.status === 'draft' ? 'warning' : 'secondary'" />
        </template>
      </Column>
      <Column header="Actions" style="width: 25%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button icon="pi pi-eye" size="small" severity="info" outlined @click="viewCourse(data.id)"
              v-tooltip.top="'View Course'" />
            <Button icon="pi pi-pencil" size="small" severity="secondary" outlined @click="editCourse(data.id)"
              v-tooltip.top="'Edit Course'" />
            <Button icon="pi pi-trash" size="small" severity="danger" outlined @click="deleteCourse(data.id)"
              v-tooltip.top="'Delete Course'" />
          </div>
        </template>
      </Column>
    </DataTable>

  </div>

  <Dialog v-model:visible="showFormDialog" modal :header="isEditMode ? 'Edit Course' : 'Create New Course'"
    :style="{ width: '50rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :draggable="false"
    :closable="true">
    <form @submit.prevent="isEditMode ? updateCourse() : createCourse()" class="space-y-6">
      <div class="grid grid-cols-1 gap-6">
        <div>
          <label for="courseName" class="block text-sm font-medium mb-2">Course Name *</label>
          <InputText id="courseName" v-model="formData.name" class="w-full" placeholder="Enter course name"
            :invalid="!!formErrors.name" required />
          <small v-if="formErrors.name" class="text-red-500">{{ formErrors.name }}</small>
        </div>

        <div>
          <label for="courseDescription" class="block text-sm font-medium mb-2">Description *</label>
          <Textarea id="courseDescription" v-model="formData.description" class="w-full" rows="4"
            placeholder="Enter course description" :invalid="!!formErrors.description" required />
          <small v-if="formErrors.description" class="text-red-500">{{ formErrors.description }}</small>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="courseLevel" class="block text-sm font-medium mb-2">Level *</label>
            <Dropdown id="courseLevel" v-model="formData.level" :options="levelOptions" option-label="label"
              option-value="value" class="w-full" placeholder="Select level" :invalid="!!formErrors.level" required />
            <small v-if="formErrors.level" class="text-red-500">{{ formErrors.level }}</small>
          </div>

          <div>
            <label for="courseDuration" class="block text-sm font-medium mb-2">Duration (hours) *</label>
            <InputNumber id="courseDuration" v-model="formData.duration" class="w-full" :min="1" :max="100"
              placeholder="Enter duration" :invalid="!!formErrors.duration" required />
            <small v-if="formErrors.duration" class="text-red-500">{{ formErrors.duration }}</small>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="courseIconUrl" class="block text-sm font-medium mb-2">Icon URL</label>
            <InputText id="courseIconUrl" v-model="formData.iconUrl" class="w-full"
              placeholder="Enter icon URL (optional)" :invalid="!!formErrors.iconUrl" />
            <small v-if="formErrors.iconUrl" class="text-red-500">{{ formErrors.iconUrl }}</small>
            <small class="text-surface-500 text-xs">URL for the course icon/logo</small>
          </div>

          <div>
            <label for="courseImageUrl" class="block text-sm font-medium mb-2">Banner Image URL</label>
            <InputText id="courseImageUrl" v-model="formData.imageUrl" class="w-full"
              placeholder="Enter banner image URL (optional)" :invalid="!!formErrors.imageUrl" />
            <small v-if="formErrors.imageUrl" class="text-red-500">{{ formErrors.imageUrl }}</small>
            <small class="text-surface-500 text-xs">URL for the course banner/cover image</small>
          </div>
        </div>

        <div v-if="isEditMode">
          <label for="courseStatus" class="block text-sm font-medium mb-2">Status</label>
          <Dropdown id="courseStatus" v-model="formData.status" :options="statusOptions" option-label="label"
            option-value="value" class="w-full" placeholder="Select status" />
        </div>
      </div>

      <div class="flex justify-end gap-3 pt-4">
        <Button type="button" label="Cancel" severity="secondary" outlined @click="cancelForm" />
        <Button type="submit" :label="isEditMode ? 'Update Course' : 'Create Course'"
          :loading="isEditMode ? isUpdating : isCreating" :disabled="isEditMode ? isUpdating : isCreating" />
      </div>
    </form>
  </Dialog>

  <Dialog v-model:visible="showViewDialog" modal header="Course Details" :style="{ width: '70rem' }"
    :breakpoints="{ '1199px': '85vw', '575px': '95vw' }" :draggable="false" :closable="true">
    <div v-if="selectedCourse">
      <TabView>
        <TabPanel header="Course Info">
          <div class="space-y-6">
            <div class="grid grid-cols-2 gap-6">
              <div>
                <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Course Title</h3>
                <p class="text-surface-700 dark:text-surface-300">{{ selectedCourse.title }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Level</h3>
                <Badge :value="selectedCourse.level"
                  :severity="selectedCourse.level === 'beginner' ? 'success' : selectedCourse.level === 'intermediate' ? 'warning' : 'danger'" />
              </div>
            </div>

            <div>
              <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Description</h3>
              <div class="bg-surface-50 dark:bg-surface-800 p-4 rounded-lg">
                <p class="text-surface-700 dark:text-surface-300">{{ selectedCourse.description }}</p>
              </div>
            </div>

            <div class="grid grid-cols-3 gap-6">
              <div>
                <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Duration</h3>
                <p class="text-surface-700 dark:text-surface-300">{{ selectedCourse.duration }} hours</p>
              </div>
              <div>
                <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Status</h3>
                <Badge :value="selectedCourse.status"
                  :severity="selectedCourse.status === 'published' ? 'success' : selectedCourse.status === 'draft' ? 'warning' : 'secondary'" />
              </div>
              <div>
                <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Lessons Count</h3>
                <Badge :value="selectedCourse.lessons?.length || 0" severity="info" />
              </div>
            </div>
          </div>
        </TabPanel>

        <TabPanel header="Lessons">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-surface-900 dark:text-surface-0">Course Lessons</h3>
              <Button
                icon="pi pi-plus"
                label="Add Lesson"
                size="small"
                @click="createLessonForCourse"
              />
            </div>

            <DataTable
              :value="courseLessons"
              :loading="loadingLessons"
              dataKey="id"
              responsiveLayout="scroll"
              :paginator="courseLessons.length > 5"
              :rows="5"
            >
              <template #empty>
                <div class="text-center py-8">
                  <i class="pi pi-book text-4xl text-surface-400 mb-4"></i>
                  <p class="text-surface-600 dark:text-surface-400">No lessons found for this course</p>
                  <Button
                    label="Create First Lesson"
                    icon="pi pi-plus"
                    class="mt-4"
                    @click="createLessonForCourse"
                  />
                </div>
              </template>

              <Column field="order" header="Order" style="width: 10%">
                <template #body="{ data }">
                  <Badge :value="data.order" />
                </template>
              </Column>
              <Column field="title" header="Title" style="width: 50%"></Column>
              <Column field="createdAt" header="Created" style="width: 20%">
                <template #body="{ data }">
                  {{ formatDate(data.createdAt) }}
                </template>
              </Column>
              <Column header="Actions" style="width: 20%">
                <template #body="{ data }">
                  <div class="flex gap-2">
                    <Button
                      icon="pi pi-eye"
                      size="small"
                      severity="info"
                      outlined
                      @click="viewLessonFromCourse(data.id)"
                      v-tooltip.top="'View Lesson'"
                    />
                    <Button
                      icon="pi pi-pencil"
                      size="small"
                      severity="secondary"
                      outlined
                      @click="editLessonFromCourse(data.id)"
                      v-tooltip.top="'Edit Lesson'"
                    />
                  </div>
                </template>
              </Column>
            </DataTable>
          </div>
        </TabPanel>
      </TabView>

      <div class="flex justify-end gap-3 pt-6 border-t border-surface-200 dark:border-surface-700 mt-6">
        <Button label="Close" severity="secondary" outlined @click="showViewDialog = false" />
        <Button label="Edit Course" @click="openEditDialog" />
      </div>
    </div>
  </Dialog>


</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useCoursesStore } from '@/stores/courses';
import { useLessonsStore } from '@/stores/lessons';

import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Badge from 'primevue/badge';
import Dialog from 'primevue/dialog';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import InputNumber from 'primevue/inputnumber';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';

const router = useRouter();
const coursesStore = useCoursesStore();
const lessonsStore = useLessonsStore();
const searchKey = ref("");

// Lessons data for course view
const courseLessons = ref<any[]>([]);
const loadingLessons = ref(false);

// Dialog states
const showFormDialog = ref(false);
const showViewDialog = ref(false);
const isEditMode = ref(false);
const isCreating = ref(false);
const isUpdating = ref(false);

// Selected course for view/edit
const selectedCourse = ref<any>(null);

// Unified form data for both create and edit
const formData = ref({
  id: '',
  name: '',
  description: '',
  level: '',
  duration: null as number | null,
  status: '',
  iconUrl: '',
  imageUrl: ''
});

// Form validation errors
const formErrors = ref({
  name: '',
  description: '',
  level: '',
  duration: '',
  iconUrl: '',
  imageUrl: ''
});

// Level options for dropdown
const levelOptions = [
  { label: 'Beginner', value: 'beginner' },
  { label: 'Intermediate', value: 'intermediate' },
  { label: 'Advanced', value: 'advanced' }
];

// Status options for dropdown
const statusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Published', value: 'published' },
  { label: 'Archived', value: 'archived' }
];

let searchTimeout: number;
watch(searchKey, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    coursesStore.searchCourses(newValue);
  }, 300);
});

const onPageChange = (event: any) => {
  const newPage = event.page + 1;
  const newLimit = event.rows;

  coursesStore.fetchCourses({
    page: newPage,
    limit: newLimit
  });
};

const viewCourse = async (courseId: string) => {
  try {
    const course = await coursesStore.fetchCourseById(courseId);
    selectedCourse.value = course;

    // Load lessons for this course
    await loadCourseLessons(courseId);

    showViewDialog.value = true;
  } catch (error) {
    console.error('Error fetching course:', error);
    // TODO: Show error message to user
  }
};

// Load lessons for a specific course
const loadCourseLessons = async (courseId: string) => {
  try {
    loadingLessons.value = true;
    courseLessons.value = await lessonsStore.fetchLessonsByCourse(courseId);
  } catch (error) {
    console.error('Error loading course lessons:', error);
    courseLessons.value = [];
  } finally {
    loadingLessons.value = false;
  }
};

// Navigate to lessons page with course filter
const createLessonForCourse = () => {
  if (selectedCourse.value) {
    router.push({
      name: 'admin-lessons',
      query: { courseId: selectedCourse.value.id, action: 'create' }
    });
  }
};

// View lesson from course view
const viewLessonFromCourse = (lessonId: string) => {
  router.push({
    name: 'admin-lessons',
    query: { lessonId, action: 'view' }
  });
};

// Edit lesson from course view
const editLessonFromCourse = (lessonId: string) => {
  router.push({
    name: 'admin-lessons',
    query: { lessonId, action: 'edit' }
  });
};

const editCourse = async (courseId: string) => {
  try {
    const course = await coursesStore.fetchCourseById(courseId);
    selectedCourse.value = course;

    // Set edit mode and populate form
    isEditMode.value = true;
    formData.value = {
      id: course.id,
      name: course.title,
      description: course.description,
      level: course.level,
      duration: course.duration,
      status: course.status,
      iconUrl: course.iconUrl || '',
      imageUrl: course.imageUrl || ''
    };

    showFormDialog.value = true;
  } catch (error) {
    console.error('Error fetching course:', error);
    // TODO: Show error message to user
  }
};

const deleteCourse = async (courseId: string) => {
  console.log('Delete course:', courseId);
  // TODO: Show confirmation dialog and delete course
  try {
    await coursesStore.deleteCourse(courseId);
    // Course will be automatically removed from the list by the store
  } catch (error) {
    console.error('Failed to delete course:', error);
    // TODO: Show error message to user
  }
};

// Form validation
const validateForm = (): boolean => {
  formErrors.value = {
    name: '',
    description: '',
    level: '',
    duration: '',
    iconUrl: '',
    imageUrl: ''
  };

  let isValid = true;

  if (!formData.value.name.trim()) {
    formErrors.value.name = 'Course name is required';
    isValid = false;
  }

  if (!formData.value.description.trim()) {
    formErrors.value.description = 'Course description is required';
    isValid = false;
  }

  if (!formData.value.level) {
    formErrors.value.level = 'Course level is required';
    isValid = false;
  }

  if (!formData.value.duration || formData.value.duration < 1) {
    formErrors.value.duration = 'Duration must be at least 1 hour';
    isValid = false;
  }

  return isValid;
};

// Create course handler
const createCourse = async () => {
  if (!validateForm()) {
    return;
  }

  isCreating.value = true;

  try {
    await coursesStore.createCourse({
      title: formData.value.name,
      description: formData.value.description,
      level: formData.value.level as 'beginner' | 'intermediate' | 'advanced',
      duration: formData.value.duration!,
      iconUrl: formData.value.iconUrl || undefined,
      imageUrl: formData.value.imageUrl || undefined
    });

    // Reset form and close dialog
    resetForm();
    showFormDialog.value = false;

  } catch (error) {
    console.error('Error creating course:', error);
    // TODO: Show error message to user
  } finally {
    isCreating.value = false;
  }
};

// Open create dialog
const openCreateDialog = () => {
  isEditMode.value = false;
  resetForm();
  showFormDialog.value = true;
};

// Cancel form handler
const cancelForm = () => {
  resetForm();
  showFormDialog.value = false;
  isEditMode.value = false;
  selectedCourse.value = null;
};

// Reset form data
const resetForm = () => {
  formData.value = {
    id: '',
    name: '',
    description: '',
    level: '',
    duration: null,
    status: '',
    iconUrl: '',
    imageUrl: ''
  };
  formErrors.value = {
    name: '',
    description: '',
    level: '',
    duration: '',
    iconUrl: '',
    imageUrl: ''
  };
};

// Helper function to format dates
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Open edit dialog from view dialog
const openEditDialog = () => {
  showViewDialog.value = false;

  // Populate edit form with selected course data
  if (selectedCourse.value) {
    isEditMode.value = true;
    formData.value = {
      id: selectedCourse.value.id,
      name: selectedCourse.value.title,
      description: selectedCourse.value.description,
      level: selectedCourse.value.level,
      duration: selectedCourse.value.duration,
      status: selectedCourse.value.status,
      iconUrl: selectedCourse.value.iconUrl || '',
      imageUrl: selectedCourse.value.imageUrl || ''
    };
  }

  showFormDialog.value = true;
};

// Update course handler
const updateCourse = async () => {
  if (!validateForm()) {
    return;
  }

  isUpdating.value = true;

  try {
    await coursesStore.updateCourse(formData.value.id, {
      title: formData.value.name,
      description: formData.value.description,
      level: formData.value.level as 'beginner' | 'intermediate' | 'advanced',
      duration: formData.value.duration!,
      status: formData.value.status as 'draft' | 'published' | 'archived',
      iconUrl: formData.value.iconUrl || undefined,
      imageUrl: formData.value.imageUrl || undefined
    });

    // Close dialog and refresh
    showFormDialog.value = false;
    selectedCourse.value = null;
    isEditMode.value = false;

  } catch (error) {
    console.error('Error updating course:', error);
    // TODO: Show error message to user
  } finally {
    isUpdating.value = false;
  }
};

onMounted(() => {
  coursesStore.fetchCourses();
});
</script>
