<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText v-model="searchKey" placeholder="Search courses..." class="w-full pl-10" />
        </IconField>
      </template>
      <template #end>
        <Button icon="pi pi-plus" @click="openCreateDialog" v-tooltip.left="'Create New Course'"
          aria-label="Create New Course" label="Create new course" />
      </template>
    </Menubar>
    <Divider />
    <DataTable :value="coursesStore.courses" :loading="coursesStore.isLoading" paginator
      :rows="coursesStore.pagination.limit" :totalRecords="coursesStore.pagination.total"
      :rowsPerPageOptions="[5, 10, 20, 50]" :first="(coursesStore.pagination.page - 1) * coursesStore.pagination.limit"
      @page="onPageChange" tableStyle="min-width: 50rem" :lazy="true" dataKey="id" responsiveLayout="scroll">
      <Column field="title" header="Course Name" style="width: 25%"></Column>
      <Column field="lessonsCount" header="Lessons" style="width: 15%"></Column>
      <Column field="duration" header="Duration (hrs)" style="width: 15%"></Column>
      <Column field="level" header="Level" style="width: 15%">
        <template #body="{ data }">
          <Badge :value="data.level"
            :severity="data.level === 'beginner' ? 'success' : data.level === 'intermediate' ? 'warning' : 'danger'" />
        </template>
      </Column>
      <Column field="status" header="Status" style="width: 15%">
        <template #body="{ data }">
          <Badge :value="data.status"
            :severity="data.status === 'published' ? 'success' : data.status === 'draft' ? 'warning' : 'secondary'" />
        </template>
      </Column>
      <Column header="Actions" style="width: 25%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button icon="pi pi-eye" size="small" severity="info" outlined @click="viewCourse(data.id)"
              v-tooltip.top="'View Course'" />
            <Button icon="pi pi-pencil" size="small" severity="secondary" outlined @click="editCourse(data.id)"
              v-tooltip.top="'Edit Course'" />
            <Button icon="pi pi-trash" size="small" severity="danger" outlined @click="deleteCourse(data.id)"
              v-tooltip.top="'Delete Course'" />
          </div>
        </template>
      </Column>
    </DataTable>

  </div>

  <Dialog v-model:visible="showFormDialog" modal :header="isEditMode ? 'Edit Course' : 'Create New Course'"
    :style="{ width: '50rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :draggable="false"
    :closable="true">
    <form @submit.prevent="isEditMode ? updateCourse() : createCourse()" class="space-y-6">
      <div class="grid grid-cols-1 gap-6">
        <div>
          <label for="courseName" class="block text-sm font-medium mb-2">Course Name *</label>
          <InputText id="courseName" v-model="formData.name" class="w-full" placeholder="Enter course name"
            :invalid="!!formErrors.name" required />
          <small v-if="formErrors.name" class="text-red-500">{{ formErrors.name }}</small>
        </div>

        <div>
          <label for="courseDescription" class="block text-sm font-medium mb-2">Description *</label>
          <Textarea id="courseDescription" v-model="formData.description" class="w-full" rows="4"
            placeholder="Enter course description" :invalid="!!formErrors.description" required />
          <small v-if="formErrors.description" class="text-red-500">{{ formErrors.description }}</small>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="courseLevel" class="block text-sm font-medium mb-2">Level *</label>
            <Dropdown id="courseLevel" v-model="formData.level" :options="levelOptions" option-label="label"
              option-value="value" class="w-full" placeholder="Select level" :invalid="!!formErrors.level" required />
            <small v-if="formErrors.level" class="text-red-500">{{ formErrors.level }}</small>
          </div>

          <div>
            <label for="courseDuration" class="block text-sm font-medium mb-2">Duration (hours) *</label>
            <InputNumber id="courseDuration" v-model="formData.duration" class="w-full" :min="1" :max="100"
              placeholder="Enter duration" :invalid="!!formErrors.duration" required />
            <small v-if="formErrors.duration" class="text-red-500">{{ formErrors.duration }}</small>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="courseIconUrl" class="block text-sm font-medium mb-2">Icon URL</label>
            <InputText id="courseIconUrl" v-model="formData.iconUrl" class="w-full"
              placeholder="Enter icon URL (optional)" :invalid="!!formErrors.iconUrl" />
            <small v-if="formErrors.iconUrl" class="text-red-500">{{ formErrors.iconUrl }}</small>
            <small class="text-surface-500 text-xs">URL for the course icon/logo</small>
          </div>

          <div>
            <label for="courseImageUrl" class="block text-sm font-medium mb-2">Banner Image URL</label>
            <InputText id="courseImageUrl" v-model="formData.imageUrl" class="w-full"
              placeholder="Enter banner image URL (optional)" :invalid="!!formErrors.imageUrl" />
            <small v-if="formErrors.imageUrl" class="text-red-500">{{ formErrors.imageUrl }}</small>
            <small class="text-surface-500 text-xs">URL for the course banner/cover image</small>
          </div>
        </div>

        <div v-if="isEditMode">
          <label for="courseStatus" class="block text-sm font-medium mb-2">Status</label>
          <Dropdown id="courseStatus" v-model="formData.status" :options="statusOptions" option-label="label"
            option-value="value" class="w-full" placeholder="Select status" />
        </div>
      </div>

      <div class="flex justify-end gap-3 pt-4">
        <Button type="button" label="Cancel" severity="secondary" outlined @click="cancelForm" />
        <Button type="submit" :label="isEditMode ? 'Update Course' : 'Create Course'"
          :loading="isEditMode ? isUpdating : isCreating" :disabled="isEditMode ? isUpdating : isCreating" />
      </div>
    </form>
  </Dialog>

  <Dialog v-model:visible="showViewDialog" modal header="Course Details" :style="{ width: '50rem' }"
    :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :draggable="false" :closable="true">
    <div v-if="selectedCourse" class="space-y-6">
      <div>
        <label class="text-sm mb-2 text-gray-400">Course title:</label>
        <p>{{ selectedCourse.title }}</p>
      </div>
      <div>
        <label class="text-sm mb-2 text-gray-400">Description:</label>
        <p>{{ selectedCourse.description }}</p>
      </div>
      <div>
        <label class="text-sm mb-2 text-gray-400">Duration:</label>
        <p>{{ selectedCourse.duration }}</p>
      </div>
      <div>
        <label class="text-sm mb-2 text-gray-400">Number of lessons:</label>
        <p>{{ selectedCourse.lessons?.length || 0 }}</p>
      </div>
      <div>
        <label class="text-sm mb-2 text-gray-400">Level:</label>
        <p>{{ selectedCourse.level }}</p>
      </div>
      <div>
        <label class="text-sm mb-2 text-gray-400">Status:</label>
        <p>{{ selectedCourse.status }}</p>
      </div>
      <div class="flex justify-end gap-3 pt-4">
        <Button label="Close" severity="secondary" outlined @click="showViewDialog = false" />
        <Button label="Edit Course" @click="openEditDialog" />
      </div>
    </div>
  </Dialog>


</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useCoursesStore } from '@/stores/courses';

import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Badge from 'primevue/badge';
import Dialog from 'primevue/dialog';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import InputNumber from 'primevue/inputnumber';

const coursesStore = useCoursesStore();
const searchKey = ref("");

// Dialog states
const showFormDialog = ref(false);
const showViewDialog = ref(false);
const isEditMode = ref(false);
const isCreating = ref(false);
const isUpdating = ref(false);

// Selected course for view/edit
const selectedCourse = ref<any>(null);

// Unified form data for both create and edit
const formData = ref({
  id: '',
  name: '',
  description: '',
  level: '',
  duration: null as number | null,
  status: '',
  iconUrl: '',
  imageUrl: ''
});

// Form validation errors
const formErrors = ref({
  name: '',
  description: '',
  level: '',
  duration: '',
  iconUrl: '',
  imageUrl: ''
});

// Level options for dropdown
const levelOptions = [
  { label: 'Beginner', value: 'beginner' },
  { label: 'Intermediate', value: 'intermediate' },
  { label: 'Advanced', value: 'advanced' }
];

// Status options for dropdown
const statusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Published', value: 'published' },
  { label: 'Archived', value: 'archived' }
];

let searchTimeout: number;
watch(searchKey, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    coursesStore.searchCourses(newValue);
  }, 300);
});

const onPageChange = (event: any) => {
  const newPage = event.page + 1;
  const newLimit = event.rows;

  coursesStore.fetchCourses({
    page: newPage,
    limit: newLimit
  });
};

const viewCourse = async (courseId: string) => {
  try {
    const course = await coursesStore.fetchCourseById(courseId);
    selectedCourse.value = course;
    showViewDialog.value = true;
  } catch (error) {
    console.error('Error fetching course:', error);
    // TODO: Show error message to user
  }
};

const editCourse = async (courseId: string) => {
  try {
    const course = await coursesStore.fetchCourseById(courseId);
    selectedCourse.value = course;

    // Set edit mode and populate form
    isEditMode.value = true;
    formData.value = {
      id: course.id,
      name: course.title,
      description: course.description,
      level: course.level,
      duration: course.duration,
      status: course.status,
      iconUrl: course.iconUrl || '',
      imageUrl: course.imageUrl || ''
    };

    showFormDialog.value = true;
  } catch (error) {
    console.error('Error fetching course:', error);
    // TODO: Show error message to user
  }
};

const deleteCourse = async (courseId: string) => {
  console.log('Delete course:', courseId);
  // TODO: Show confirmation dialog and delete course
  try {
    await coursesStore.deleteCourse(courseId);
    // Course will be automatically removed from the list by the store
  } catch (error) {
    console.error('Failed to delete course:', error);
    // TODO: Show error message to user
  }
};

// Form validation
const validateForm = (): boolean => {
  formErrors.value = {
    name: '',
    description: '',
    level: '',
    duration: '',
    iconUrl: '',
    imageUrl: ''
  };

  let isValid = true;

  if (!formData.value.name.trim()) {
    formErrors.value.name = 'Course name is required';
    isValid = false;
  }

  if (!formData.value.description.trim()) {
    formErrors.value.description = 'Course description is required';
    isValid = false;
  }

  if (!formData.value.level) {
    formErrors.value.level = 'Course level is required';
    isValid = false;
  }

  if (!formData.value.duration || formData.value.duration < 1) {
    formErrors.value.duration = 'Duration must be at least 1 hour';
    isValid = false;
  }

  return isValid;
};

// Create course handler
const createCourse = async () => {
  if (!validateForm()) {
    return;
  }

  isCreating.value = true;

  try {
    await coursesStore.createCourse({
      title: formData.value.name,
      description: formData.value.description,
      level: formData.value.level as 'beginner' | 'intermediate' | 'advanced',
      duration: formData.value.duration!,
      iconUrl: formData.value.iconUrl || undefined,
      imageUrl: formData.value.imageUrl || undefined
    });

    // Reset form and close dialog
    resetForm();
    showFormDialog.value = false;

  } catch (error) {
    console.error('Error creating course:', error);
    // TODO: Show error message to user
  } finally {
    isCreating.value = false;
  }
};

// Open create dialog
const openCreateDialog = () => {
  isEditMode.value = false;
  resetForm();
  showFormDialog.value = true;
};

// Cancel form handler
const cancelForm = () => {
  resetForm();
  showFormDialog.value = false;
  isEditMode.value = false;
  selectedCourse.value = null;
};

// Reset form data
const resetForm = () => {
  formData.value = {
    id: '',
    name: '',
    description: '',
    level: '',
    duration: null,
    status: '',
    iconUrl: '',
    imageUrl: ''
  };
  formErrors.value = {
    name: '',
    description: '',
    level: '',
    duration: '',
    iconUrl: '',
    imageUrl: ''
  };
};

// Helper function to format dates
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Open edit dialog from view dialog
const openEditDialog = () => {
  showViewDialog.value = false;

  // Populate edit form with selected course data
  if (selectedCourse.value) {
    isEditMode.value = true;
    formData.value = {
      id: selectedCourse.value.id,
      name: selectedCourse.value.title,
      description: selectedCourse.value.description,
      level: selectedCourse.value.level,
      duration: selectedCourse.value.duration,
      status: selectedCourse.value.status,
      iconUrl: selectedCourse.value.iconUrl || '',
      imageUrl: selectedCourse.value.imageUrl || ''
    };
  }

  showFormDialog.value = true;
};

// Update course handler
const updateCourse = async () => {
  if (!validateForm()) {
    return;
  }

  isUpdating.value = true;

  try {
    await coursesStore.updateCourse(formData.value.id, {
      title: formData.value.name,
      description: formData.value.description,
      level: formData.value.level as 'beginner' | 'intermediate' | 'advanced',
      duration: formData.value.duration!,
      status: formData.value.status as 'draft' | 'published' | 'archived',
      iconUrl: formData.value.iconUrl || undefined,
      imageUrl: formData.value.imageUrl || undefined
    });

    // Close dialog and refresh
    showFormDialog.value = false;
    selectedCourse.value = null;
    isEditMode.value = false;

  } catch (error) {
    console.error('Error updating course:', error);
    // TODO: Show error message to user
  } finally {
    isUpdating.value = false;
  }
};

onMounted(() => {
  coursesStore.fetchCourses();
});
</script>
