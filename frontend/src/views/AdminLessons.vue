<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Lessons Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage course lessons
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <div class="flex gap-4 items-center">
          <IconField>
            <InputIcon class="pi pi-search" />
            <InputText v-model="searchKey" placeholder="Search lessons..." class="w-full pl-10" />
          </IconField>
          <Dropdown v-model="selectedCourseFilter" :options="courseOptions" option-label="label" option-value="value"
            placeholder="Filter by course" class="w-64" show-clear />
        </div>
      </template>
      <template #end>
        <div class="flex gap-2">
          <Button icon="pi pi-trash" severity="danger" outlined @click="bulkDelete" v-tooltip.left="'Delete Selected'"
            :disabled="!selectedLessons.length" label="Delete Selected" />
          <Button icon="pi pi-plus" @click="openCreateDialog" v-tooltip.left="'Create New Lesson'"
            aria-label="Create New Lesson" label="Create new lesson" />
        </div>
      </template>
    </Menubar>
    <Divider />
    <DataTable v-model:selection="selectedLessons" :value="lessonsStore.lessons" :loading="lessonsStore.isLoading"
      paginator :rows="lessonsStore.pagination.limit" :totalRecords="lessonsStore.pagination.total"
      :rowsPerPageOptions="[5, 10, 20, 50]" :first="(lessonsStore.pagination.page - 1) * lessonsStore.pagination.limit"
      @page="onPageChange" tableStyle="min-width: 50rem" :lazy="true" dataKey="id" responsiveLayout="scroll"
      selection-mode="multiple">
      <Column selection-mode="multiple" header-style="width: 3rem"></Column>
      <Column field="title" header="Lesson Title" style="width: 30%"></Column>
      <Column field="course.title" header="Course" style="width: 20%">
        <template #body="{ data }">
          <p>{{ data.course?.title || 'Unknown' }}</p>
        </template>
      </Column>
      <Column field="order" header="Order" style="width: 10%">
        <template #body="{ data }">
          <Badge :value="data.order" />
        </template>
      </Column>
      <Column field="createdAt" header="Created" style="width: 15%">
        <template #body="{ data }">
          {{ formatDate(data.createdAt) }}
        </template>
      </Column>
      <Column header="Actions" style="width: 22%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button icon="pi pi-eye" size="small" severity="info" outlined @click="viewLesson(data.id)"
              v-tooltip.top="'View Lesson'" />
            <Button icon="pi pi-pencil" size="small" severity="secondary" outlined @click="editLesson(data.id)"
              v-tooltip.top="'Edit Lesson'" />
            <Button icon="pi pi-copy" size="small" severity="help" outlined @click="duplicateLesson(data.id)"
              v-tooltip.top="'Duplicate Lesson'" />
            <Button icon="pi pi-trash" size="small" severity="danger" outlined @click="deleteLesson(data.id)"
              v-tooltip.top="'Delete Lesson'" />
          </div>
        </template>
      </Column>
    </DataTable>
  </div>

  <!-- Create/Edit Dialog -->
  <Dialog v-model:visible="showFormDialog" modal :header="isEditMode ? 'Edit Lesson' : 'Create New Lesson'"
    :style="{ width: '60rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :draggable="false"
    :closable="true">
    <form @submit.prevent="isEditMode ? updateLesson() : createLesson()" class="space-y-6">
      <div class="grid grid-cols-1 gap-6">
        <div>
          <label for="lessonCourse" class="block text-sm font-medium mb-2">Course *</label>
          <Dropdown id="lessonCourse" v-model="formData.courseId" :options="courseOptions" option-label="label"
            option-value="value" class="w-full" placeholder="Select course" :invalid="!!formErrors.courseId" required />
          <small v-if="formErrors.courseId" class="text-red-500">{{ formErrors.courseId }}</small>
        </div>

        <div>
          <label for="lessonTitle" class="block text-sm font-medium mb-2">Lesson Title *</label>
          <InputText id="lessonTitle" v-model="formData.title" class="w-full" placeholder="Enter lesson title"
            :invalid="!!formErrors.title" required />
          <small v-if="formErrors.title" class="text-red-500">{{ formErrors.title }}</small>
        </div>

        <div>
          <label for="lessonContent" class="block text-sm font-medium mb-2">Content *</label>
          <Textarea id="lessonContent" v-model="formData.content" class="w-full" rows="8"
            placeholder="Enter lesson content" :invalid="!!formErrors.content" required />
          <small v-if="formErrors.content" class="text-red-500">{{ formErrors.content }}</small>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="lessonThumbnailUrl" class="block text-sm font-medium mb-2">Thumbnail URL</label>
            <InputText id="lessonThumbnailUrl" v-model="formData.thumbnailUrl" class="w-full"
              placeholder="Enter thumbnail URL" />
          </div>

          <div>
            <label for="lessonIllustrationUrl" class="block text-sm font-medium mb-2">Illustration URL</label>
            <InputText id="lessonIllustrationUrl" v-model="formData.illustrationUrl" class="w-full"
              placeholder="Enter illustration URL" />
          </div>
        </div>

        <div>
          <label for="lessonOrder" class="block text-sm font-medium mb-2">Order</label>
          <InputNumber id="lessonOrder" v-model="formData.order" class="w-full" :min="0"
            placeholder="Enter lesson order (optional)" />
          <small class="text-surface-500">Leave empty to auto-assign order</small>
        </div>
      </div>

      <div class="flex justify-end gap-3 pt-4">
        <Button type="button" label="Cancel" severity="secondary" outlined @click="closeFormDialog" />
        <Button type="submit" :label="isEditMode ? 'Update Lesson' : 'Create Lesson'"
          :loading="isCreating || isUpdating" :disabled="isCreating || isUpdating" />
      </div>
    </form>
  </Dialog>

  <!-- View Dialog -->
  <Dialog v-model:visible="showViewDialog" modal header="Lesson Details" :style="{ width: '60rem' }"
    :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :draggable="false" :closable="true">
    <div v-if="selectedLesson" class="space-y-6">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Course</h3>
          <Badge :value="selectedLesson.course?.title || 'Unknown'" severity="info" />
        </div>
        <div>
          <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Order</h3>
          <Badge :value="selectedLesson.order" />
        </div>
      </div>

      <div>
        <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Title</h3>
        <p class="text-surface-700 dark:text-surface-300">{{ selectedLesson.title }}</p>
      </div>

      <div>
        <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Content</h3>
        <div class="bg-surface-50 dark:bg-surface-800 p-4 rounded-lg">
          <p class="text-surface-700 dark:text-surface-300 whitespace-pre-wrap">{{ selectedLesson.content }}</p>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4" v-if="selectedLesson.thumbnailUrl || selectedLesson.illustrationUrl">
        <div v-if="selectedLesson.thumbnailUrl">
          <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Thumbnail</h3>
          <img :src="selectedLesson.thumbnailUrl" alt="Thumbnail" class="w-full h-32 object-cover rounded-lg" />
        </div>
        <div v-if="selectedLesson.illustrationUrl">
          <h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-2">Illustration</h3>
          <img :src="selectedLesson.illustrationUrl" alt="Illustration" class="w-full h-32 object-cover rounded-lg" />
        </div>
      </div>

      <div class="flex justify-end gap-3 pt-4">
        <Button label="Edit" icon="pi pi-pencil" @click="openEditDialog" />
        <Button label="Close" severity="secondary" outlined @click="showViewDialog = false" />
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useLessonsStore } from '@/stores/lessons';
import { useCoursesStore } from '@/stores/courses';
import type { Lesson } from '@/features/lessons/services/lessonService';

// PrimeVue components
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import InputNumber from 'primevue/inputnumber';
import Dropdown from 'primevue/dropdown';
import Badge from 'primevue/badge';
import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';

// Stores
const lessonsStore = useLessonsStore();
const coursesStore = useCoursesStore();

// Reactive state
const searchKey = ref('');
const selectedCourseFilter = ref('');
const selectedLessons = ref<Lesson[]>([]);
const selectedLesson = ref<Lesson | null>(null);
const showFormDialog = ref(false);
const showViewDialog = ref(false);
const isEditMode = ref(false);
const isCreating = ref(false);
const isUpdating = ref(false);

// Form data
const formData = reactive({
  id: '',
  courseId: '',
  title: '',
  content: '',
  thumbnailUrl: '',
  illustrationUrl: '',
  order: undefined as number | undefined
});

// Form errors
const formErrors = reactive({
  courseId: '',
  title: '',
  content: ''
});

// Computed
const courseOptions = computed(() => [
  { label: 'All Courses', value: '' },
  ...coursesStore.courses.map(course => ({
    label: course.title,
    value: course.id
  }))
]);

// Watchers
watch(searchKey, (newValue) => {
  if (newValue.length >= 3 || newValue.length === 0) {
    lessonsStore.searchLessons(newValue);
  }
});

watch(selectedCourseFilter, (newValue) => {
  lessonsStore.filterByCourse(newValue);
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const onPageChange = (event: any) => {
  lessonsStore.changePage(event.page + 1);
};

const resetForm = () => {
  formData.id = '';
  formData.courseId = '';
  formData.title = '';
  formData.content = '';
  formData.thumbnailUrl = '';
  formData.illustrationUrl = '';
  formData.order = undefined;

  formErrors.courseId = '';
  formErrors.title = '';
  formErrors.content = '';
};

const validateForm = () => {
  let isValid = true;

  // Reset errors
  formErrors.courseId = '';
  formErrors.title = '';
  formErrors.content = '';

  if (!formData.courseId) {
    formErrors.courseId = 'Course is required';
    isValid = false;
  }

  if (!formData.title.trim()) {
    formErrors.title = 'Title is required';
    isValid = false;
  }

  if (!formData.content.trim()) {
    formErrors.content = 'Content is required';
    isValid = false;
  }

  return isValid;
};

const openCreateDialog = () => {
  resetForm();
  isEditMode.value = false;
  showFormDialog.value = true;
};

const closeFormDialog = () => {
  showFormDialog.value = false;
  resetForm();
  selectedLesson.value = null;
  isEditMode.value = false;
};

const viewLesson = async (lessonId: string) => {
  try {
    const lesson = await lessonsStore.fetchLessonById(lessonId);
    selectedLesson.value = lesson;
    showViewDialog.value = true;
  } catch (error) {
    console.error('Error fetching lesson:', error);
  }
};

const editLesson = async (lessonId: string) => {
  try {
    const lesson = await lessonsStore.fetchLessonById(lessonId);
    selectedLesson.value = lesson;

    // Set edit mode and populate form
    isEditMode.value = true;
    formData.id = lesson.id;
    formData.courseId = lesson.courseId;
    formData.title = lesson.title;
    formData.content = lesson.content;
    formData.thumbnailUrl = lesson.thumbnailUrl || '';
    formData.illustrationUrl = lesson.illustrationUrl || '';
    formData.order = lesson.order;

    showFormDialog.value = true;
  } catch (error) {
    console.error('Error fetching lesson:', error);
  }
};

const createLesson = async () => {
  if (!validateForm()) {
    return;
  }

  isCreating.value = true;

  try {
    await lessonsStore.createLesson({
      courseId: formData.courseId,
      title: formData.title,
      content: formData.content,
      thumbnailUrl: formData.thumbnailUrl || undefined,
      illustrationUrl: formData.illustrationUrl || undefined,
      order: formData.order
    });

    closeFormDialog();
  } catch (error) {
    console.error('Error creating lesson:', error);
  } finally {
    isCreating.value = false;
  }
};

const updateLesson = async () => {
  if (!validateForm()) {
    return;
  }

  isUpdating.value = true;

  try {
    await lessonsStore.updateLesson(formData.id, {
      courseId: formData.courseId,
      title: formData.title,
      content: formData.content,
      thumbnailUrl: formData.thumbnailUrl || undefined,
      illustrationUrl: formData.illustrationUrl || undefined,
      order: formData.order
    });

    closeFormDialog();
  } catch (error) {
    console.error('Error updating lesson:', error);
  } finally {
    isUpdating.value = false;
  }
};

const deleteLesson = async (lessonId: string) => {
  if (confirm('Are you sure you want to delete this lesson?')) {
    try {
      await lessonsStore.deleteLesson(lessonId);
    } catch (error) {
      console.error('Error deleting lesson:', error);
    }
  }
};

const duplicateLesson = async (lessonId: string) => {
  try {
    await lessonsStore.duplicateLesson(lessonId);
  } catch (error) {
    console.error('Error duplicating lesson:', error);
  }
};

const bulkDelete = async () => {
  if (selectedLessons.value.length === 0) {
    return;
  }

  const lessonIds = selectedLessons.value.map(lesson => lesson.id);

  if (confirm(`Are you sure you want to delete ${lessonIds.length} lesson(s)?`)) {
    try {
      await lessonsStore.bulkDeleteLessons({ lessonIds });
      selectedLessons.value = [];
    } catch (error) {
      console.error('Error bulk deleting lessons:', error);
    }
  }
};

const openEditDialog = () => {
  showViewDialog.value = false;

  if (selectedLesson.value) {
    isEditMode.value = true;
    formData.id = selectedLesson.value.id;
    formData.courseId = selectedLesson.value.courseId;
    formData.title = selectedLesson.value.title;
    formData.content = selectedLesson.value.content;
    formData.thumbnailUrl = selectedLesson.value.thumbnailUrl || '';
    formData.illustrationUrl = selectedLesson.value.illustrationUrl || '';
    formData.order = selectedLesson.value.order;
  }

  showFormDialog.value = true;
};

const route = useRoute();
// Handle query parameters for navigation from course view
const handleQueryParams = async () => {
  const { courseId, lessonId, action } = route.query;

  if (courseId && action === 'create') {

    console.log("create requested from the admin course page")

    // Pre-fill course in create form
    selectedCourseFilter.value = courseId as string;
    await lessonsStore.filterByCourse(courseId as string);

    // Open create dialog with pre-selected course
    formData.courseId = courseId as string;
    openCreateDialog();
  } else if (lessonId && (action === 'view' || action === 'edit')) {
    // Load specific lesson
    try {
      const lesson = await lessonsStore.fetchLessonById(lessonId as string);
      if (action === 'view') {
        selectedLesson.value = lesson;
        showViewDialog.value = true;
      } else if (action === 'edit') {
        await editLesson(lessonId as string);
      }
    } catch (error) {
      console.error('Error handling lesson query params:', error);
    }
  }
};

// Lifecycle
onMounted(async () => {
  await Promise.all([
    lessonsStore.fetchLessons(),
    coursesStore.fetchCourses()
  ]);

  // Handle query parameters after data is loaded
  await handleQueryParams();
});
</script>
