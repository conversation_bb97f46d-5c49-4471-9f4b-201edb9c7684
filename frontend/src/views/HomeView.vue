<template>
  <div class="min-h-screen bg-white text-black">
    <main class="flex-1 flex flex-col items-center">
      <div class="w-full h-screen max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 bg-fit bg-center bg-no-repeat"
        style="background-image: url('/images/home/<USER>')">
        <div class="h-1/10">
          <NavBar />
        </div>
        <div class="h-8/10">
          <MainSection />
        </div>
        <div class="h-1/10">
          <Footer />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import NavBar from '@/components/home/<USER>';
import MainSection from '@/components/home/<USER>';
</script>
