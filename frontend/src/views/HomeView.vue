<template>
  <main class="min-h-screen bg-white">
    <NavBar />

    <!-- Main Content Container with <PERSON> Width -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-8 sm:py-12 lg:py-16">
        <!-- Hero Section -->
        <section class="text-center mb-12 lg:mb-16">
          <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Learn Rwanda Traffic Rules
          </h1>
          <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Master the road rules of Rwanda with our interactive learning platform.
            Practice, learn, and become a confident driver.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              label="Start Learning"
              class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 text-lg"
            />
            <Button
              label="View Courses"
              severity="secondary"
              class="px-6 py-3 text-lg"
            />
          </div>
        </section>

        <!-- Features Section -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-12 lg:mb-16">
          <div class="bg-gray-50 p-6 rounded-lg text-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <i class="pi pi-book text-blue-600 text-xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Interactive Lessons</h3>
            <p class="text-gray-600">Learn with engaging, interactive content designed for effective learning.</p>
          </div>

          <div class="bg-gray-50 p-6 rounded-lg text-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <i class="pi pi-check-circle text-green-600 text-xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Practice Tests</h3>
            <p class="text-gray-600">Test your knowledge with comprehensive practice exams and quizzes.</p>
          </div>

          <div class="bg-gray-50 p-6 rounded-lg text-center md:col-span-2 lg:col-span-1">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <i class="pi pi-trophy text-purple-600 text-xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Track Progress</h3>
            <p class="text-gray-600">Monitor your learning progress and earn achievements along the way.</p>
          </div>
        </section>

        <!-- CTA Section -->
        <section class="bg-blue-50 rounded-xl p-8 lg:p-12 text-center">
          <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
            Ready to Start Your Journey?
          </h2>
          <p class="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
            Join thousands of learners who have successfully mastered Rwanda's traffic rules with our platform.
          </p>
          <Button
            label="Get Started Today"
            class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
          />
        </section>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
import NavBar from '@/components/NavBar.vue';
import Button from 'primevue/button';
</script>
