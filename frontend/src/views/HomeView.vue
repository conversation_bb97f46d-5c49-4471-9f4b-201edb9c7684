<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation Header -->
    <header class="w-full bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto">
        <NavBar />
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-12 sm:py-16 lg:py-20">

          <!-- Hero Section -->
          <section class="text-center mb-16 lg:mb-20">
            <div class="max-w-4xl mx-auto">
              <div class="space-y-8">
                <div class="space-y-6">
                  <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                    Learn Rwanda Traffic Rules
                  </h1>
                  <p class="text-xl sm:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Master the road rules of Rwanda with our interactive learning platform.
                    Practice, learn, and become a confident driver.
                  </p>
                </div>

                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
                  <Button
                    label="Start Learning"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                  />
                  <Button
                    label="View Courses"
                    severity="secondary"
                    class="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-200 w-full sm:w-auto"
                  />
                </div>
              </div>
            </div>
          </section>

          <!-- Features Section -->
          <section class="mb-16 lg:mb-20">
            <div class="max-w-6xl mx-auto">
              <div class="text-center mb-12">
                <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                  Why Choose Our Platform?
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                  Everything you need to master Rwanda's traffic rules in one place
                </p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
                <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200 w-full max-w-sm">
                  <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i class="pi pi-book text-blue-600 text-2xl"></i>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Interactive Lessons</h3>
                  <p class="text-gray-600 text-center leading-relaxed">
                    Learn with engaging, interactive content designed for effective learning and retention.
                  </p>
                </div>

                <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200 w-full max-w-sm">
                  <div class="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i class="pi pi-check-circle text-green-600 text-2xl"></i>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Practice Tests</h3>
                  <p class="text-gray-600 text-center leading-relaxed">
                    Test your knowledge with comprehensive practice exams and real-world scenarios.
                  </p>
                </div>

                <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200 w-full max-w-sm md:col-span-2 lg:col-span-1 md:mx-auto">
                  <div class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <i class="pi pi-trophy text-purple-600 text-2xl"></i>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-900 mb-3 text-center">Track Progress</h3>
                  <p class="text-gray-600 text-center leading-relaxed">
                    Monitor your learning journey and earn achievements as you progress.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <!-- CTA Section -->
          <section class="flex justify-center">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 lg:p-12 text-center max-w-4xl w-full text-white">
              <h2 class="text-3xl sm:text-4xl font-bold mb-6">
                Ready to Start Your Journey?
              </h2>
              <p class="text-xl mb-8 max-w-2xl mx-auto leading-relaxed opacity-90">
                Join thousands of learners who have successfully mastered Rwanda's traffic rules with our platform.
              </p>
              <div class="flex justify-center">
                <Button
                  label="Get Started Today"
                  class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                />
              </div>
            </div>
          </section>

        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import NavBar from '@/components/NavBar.vue';
</script>
