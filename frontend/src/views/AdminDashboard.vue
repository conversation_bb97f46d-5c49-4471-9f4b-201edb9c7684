<template>
  <div class="h-screen flex dark-mode">
    <div class="w-64 bg-surface-900 dark:bg-surface-950 flex flex-col">
      <SideMenu />
    </div>

    <div class="flex-1 flex flex-col overflow-y-scroll">
      <main class="flex-1 p-6 bg-surface-50 dark:bg-surface-800">
        <div class="max-w-7xl mx-auto">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import SideMenu from '@/components/admin/SideMenu.vue'
</script>
