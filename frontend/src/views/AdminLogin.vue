<template>
  <div class="h-screen w-full flex items-center justify-center">
    <Card class="w-md bg-gray">
      <template #title>
        <h2 class="text-2xl font-bold">Get started</h2>
      </template>
      <template #subtitle>
        <p>Log into your account</p>
      </template>
      <template #content>
        <AdminLoginForm />
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card } from 'primevue';
import AdminLoginForm from '@/components/AdminLoginForm.vue';
</script>
<style scoped>
.bg-gray {
  background-color: rgb(31, 30, 30);
}
</style>